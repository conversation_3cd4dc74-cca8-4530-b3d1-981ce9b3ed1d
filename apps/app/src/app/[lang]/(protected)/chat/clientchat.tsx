"use client"
import React, { useRef, useState } from "react"
import Image from "next/image"
import { Eye, Upload } from "lucide-react"

import { ChatInput } from "@/components/chatcomponents/chatinput"
import { ChatMessage } from "@/components/chatcomponents/chatmessage"
import { type Message, useChat } from "@ai-sdk/react"
interface EnhancedMessage extends Message {
  fileData?: {
    name: string
    type: string
    url: string
    summary?: string
  }
}

interface SummaryResponse {
  summary: string
  extractedText: string
  fileName: string
  fileType: string
}

export default function ClientChat() {
  const { messages, input, handleInputChange, append, isLoading } = useChat({
    api: "/api/chat",
  })

  const [uploadedFile, setUploadedFile] = useState<File | null>(null)
  const [, setExtractedContent] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const fileCache = useRef<Record<string, string>>({})

  const handleFileUpload = (file: File) => {
    setUploadedFile(file)
  }

  const triggerFileInput = () => {
    fileInputRef.current?.click()
  }

  const handleFullSubmit = async () => {
    const fileToProcess = uploadedFile
    setUploadedFile(null)
    const userInput = input.trim()
    handleInputChange({ target: { value: "" } } as React.ChangeEvent<HTMLTextAreaElement>)

    if (fileToProcess) {
      const fileId = `${Date.now()}-${fileToProcess.name}`
      const fileData = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = (e) => resolve(e.target?.result as string)
        reader.onerror = reject
        reader.readAsDataURL(fileToProcess)
      })

      fileCache.current[fileId] = fileData

      try {
        const formData = new FormData()
        formData.append("file", fileToProcess)

        const response = await fetch("/api/test", {
          method: "POST",
          body: formData,
        })

        if (!response.ok) {
          throw new Error(`Erreur HTTP: ${response.status}`)
        }

        const data = (await response.json()) as SummaryResponse
        const summary = data.summary || "Résumé non disponible"
        const extractedText = data.extractedText || ""

        setExtractedContent(extractedText) // Stocke le contenu extrait

        await append({
          content: userInput || "",
          role: "user",
          data: {
            hiddenContent: extractedText,
            file: {
              id: fileId,
              name: data.fileName || fileToProcess.name,
              type: data.fileType || fileToProcess.type,
              summary: summary,
            },
          },
        })
      } catch (error) {
        console.error("Erreur d'extraction:", error)
        await append({
          content: "Erreur lors de l'analyse du document",
          role: "user",
          data: {
            file: {
              id: `${Date.now()}-error`,
              name: fileToProcess.name,
              type: fileToProcess.type,
              summary: "Erreur lors de la génération du résumé",
            },
          },
        })
      }
    } else if (userInput) {
      await append({
        content: userInput,
        role: "user",
        data: {
          hiddenContent: "", // Réinitialise à une chaîne vide au lieu d'utiliser extractedContent
        },
      })
      setExtractedContent(null) // Réinitialise le contenu extrait
    }
  }

  const filteredMessages = messages.filter(
    (msg) =>
      (msg.role === "user" || msg.role === "assistant") &&
      "content" in msg &&
      !(msg.data as unknown as { fileContent: File })?.fileContent
  )

  const enhancedMessages: EnhancedMessage[] = filteredMessages.map((msg) => {
    const fileInfo = (msg.data as unknown as { file: { id: string; summary: string; name: string; type: string } })
      ?.file
    if (fileInfo?.id && fileCache.current[fileInfo.id]) {
      return {
        ...msg,
        fileData: {
          name: fileInfo.name,
          type: fileInfo.type,
          url: fileCache.current[fileInfo.id],
          summary: fileInfo.summary,
        },
      }
    }
    return msg
  })

  const isChatEmpty = enhancedMessages.length === 0

  const openFileInNewTab = (fileData: { url: string; type: string; name: string }) => {
    const newWindow = window.open("", "_blank")
    if (!newWindow) return

    if (fileData.type.startsWith("image/")) {
      newWindow.document.write(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>${fileData.name}</title>
            <style>
              body { margin: 0; display: flex; justify-content: center; align-items: center; height: 100vh; }
              img { max-width: 100%; max-height: 100%; object-fit: contain; }
            </style>
          </head>
          <body>
            <img src="${fileData.url}" alt="${fileData.name}" />
          </body>
        </html>
      `)
    } else {
      newWindow.document.write(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>${fileData.name}</title>
            <style>
              body { margin: 0; height: 100vh; }
              embed { width: 100%; height: 100%; }
            </style>
          </head>
          <body>
            <embed src="${fileData.url}" type="${fileData.type}" />
          </body>
        </html>
      `)
    }
    newWindow.document.close()
  }

  return (
    <div className="flex h-screen items-center justify-center bg-gray-100 px-2">
      <div className="flex size-full max-w-2xl flex-col">
        <div className="flex-1 overflow-y-auto px-4 py-2 pb-24 scrollbar-hide">
          {isChatEmpty ? (
            <div className="flex h-full flex-1 flex-col items-center justify-center gap-4 text-center">
              <Image src={`/robot.png?${Date.now()}`} alt="Robot" width={50} height={50} />
              <div className="space-y-[0.5px]">
                <h2 className="text-xl font-bold text-black">Bienvenue sur votre assistant d&rsquo;analyse de DCE !</h2>
                <p className="text-sm text-gray-500">
                  Pour commencer, importez simplement votre DCE (PDF, DOCS ou TXT). J&rsquo;analyserai <br /> son
                  contenu et je pourrai ensuite répondre à vos questions.
                </p>
              </div>
              <button
                onClick={triggerFileInput}
                className="flex items-center gap-2 rounded-full bg-blue-600 px-4 py-2 font-medium text-white transition-colors hover:bg-blue-700"
              >
                <Upload size={16} />
                Analyser le document
              </button>
              <input
                type="file"
                ref={fileInputRef}
                onChange={(e) => e.target.files?.[0] && handleFileUpload(e.target.files[0])}
                className="hidden"
                accept=".pdf,.doc,.docx,.txt,.png,.jpg,.jpeg"
              />
            </div>
          ) : (
            <div className="flex flex-col items-center gap-4 pb-4">
              {" "}
              {/* Added items-center here */}
              {enhancedMessages.map((msg, index) => (
                <div key={index} className="flex w-full max-w-3xl flex-col gap-2">
                  {" "}
                  {/* Added w-full max-w-3xl */}
                  {msg.fileData && (
                    <div className="group relative mb-2 rounded-lg border border-gray-200 bg-white p-3">
                      <div className="flex flex-col gap-2">
                        <p className="text-lg font-bold text-black">Information du Document</p>
                        <p className="text-black">
                          <span className="font-bold">Nom du document :</span> {msg.fileData.name}
                        </p>

                        {msg.fileData.summary && (
                          <div className="mt-2 rounded-md bg-gray-50 p-3">
                            <p className="mb-1 text-sm font-bold text-gray-700">Résumé du document:</p>
                            <p className="text-sm text-gray-600">{msg.fileData.summary}</p>
                          </div>
                        )}

                        <p className="flex justify-between text-sm text-black">
                          <span>
                            <span className="font-bold">Date d&rsquo;analyse:</span> {new Date().toLocaleString()}
                          </span>
                          <span>
                            <span className="font-bold">Status:</span> Analyse terminée
                          </span>
                        </p>
                        <div className="mt-2">
                          <button
                            onClick={() => openFileInNewTab(msg.fileData!)}
                            className="flex items-center gap-1 text-sm text-blue-600 hover:underline"
                            title="Voir le document"
                          >
                            <Eye size={16} />
                            Voir le document
                          </button>
                        </div>
                      </div>
                    </div>
                  )}
                  <ChatMessage role={msg.role as "user" | "assistant"} content={msg.content as string} />
                </div>
              ))}
              {isLoading && <ChatMessage role="assistant" content="⏳ Rédaction de la réponse..." />}
            </div>
          )}
        </div>

        <div className="sticky bottom-0 flex justify-center border-t bg-gray-100 py-2">
          <ChatInput
            input={input}
            onInputChange={handleInputChange}
            onSubmit={handleFullSubmit}
            onFileUpload={handleFileUpload}
            uploadedFile={uploadedFile}
            onRemoveFile={() => setUploadedFile(null)}
          />
        </div>
      </div>
    </div>
  )
}
