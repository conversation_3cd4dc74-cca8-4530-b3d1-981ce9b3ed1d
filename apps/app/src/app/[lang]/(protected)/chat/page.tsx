import React from "react"

import { Sidebar } from "@/components/chatcomponents/sidebar"
import { Topbar } from "@/components/chatcomponents/topbar"

import ClientChat from "./clientchat"

export default function ChatPage() {
  return (
    <div className="flex h-screen overflow-hidden">
      {/* Sidebar - cachée sur mobile par défaut, visible via overlay */}
      <Sidebar />

      {/* Zone principale qui prend toute la largeur sur mobile */}
      <main className="flex w-full flex-1 flex-col overflow-hidden bg-[#F1F2F3] md:w-auto">
        {/* TOPBAR FIXE avec padding pour le bouton mobile */}
        <div className="sticky top-0 z-40 pl-16 md:pl-0">
          <Topbar />
        </div>

        {/* CLIENT CHAT */}
        <ClientChat />
      </main>
    </div>
  )
}
