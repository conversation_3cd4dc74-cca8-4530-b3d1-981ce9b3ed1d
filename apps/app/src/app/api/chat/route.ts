import { type NextRequest } from "next/server"
import { streamText } from "ai"

import { openai } from "@ai-sdk/openai"

//interface MessageData {
// hiddenContent?: string
// ... autres propriétés possibles de data
//}

export const maxDuration = 30

export async function POST(req: NextRequest) {
  const body = (await req.json()) as {
    messages: { role: "user" | "assistant"; content: string; data?: unknown }[]
  }

  // Fusionner content + hiddenContent pour chaque message
  const enrichedMessages = body.messages.map((msg) => {
    let hidden = ""
    if (
      msg.data &&
      typeof msg.data === "object" &&
      "hiddenContent" in msg.data &&
      typeof msg.data.hiddenContent === "string"
    ) {
      hidden = msg.data.hiddenContent
    }
    return {
      role: msg.role,
      content: hidden ? `${msg.content}\n\n${hidden}` : msg.content,
    }
  })

  const result = await streamText({
    model: openai("gpt-4o"),
    messages: enrichedMessages,
  })

  return result.toDataStreamResponse()
}
