import { NextResponse } from "next/server"
import { NextRequest } from "next/server"
import { Fields, File as FormidableFile, Files, IncomingForm } from "formidable"
import fsSync from "fs" // pour créer le dossier si nécessaire
import fs from "fs/promises"
import { IncomingMessage } from "http"
import mammoth from "mammoth"
import path from "path"
import pdfParse from "pdf-parse"

export async function POST(request: NextRequest) {
  // Utilisez NextRequest pour la compatibilité avec l'API Route
  const form = new IncomingForm({
    uploadDir: path.join(process.cwd(), "/public/uploads"),
    keepExtensions: true,
  })

  // Crée le dossier s'il n'existe pas
  const uploadDir = path.join(process.cwd(), "/public/uploads")
  if (!fsSync.existsSync(uploadDir)) {
    fsSync.mkdirSync(uploadDir, { recursive: true })
  }

  return new Promise<Response>((resolve) => {
    form.parse(request as unknown as IncomingMessage, async (err: Error, fields: Fields, files: Files) => {
      if (err) {
        console.error("Erreur formidable:", err)
        return resolve(NextResponse.json({ error: "Erreur de téléchargement" }, { status: 500 }))
      }

      const uploadedFile = files.file?.[0] as FormidableFile | undefined

      if (!uploadedFile) {
        return resolve(NextResponse.json({ error: "Aucun fichier reçu" }, { status: 400 }))
      }

      const filePath = uploadedFile.filepath
      const fileName = path.basename(filePath)
      const fileType = uploadedFile.mimetype || "unknown"

      let extractedText = ""
      try {
        if (fileType === "application/pdf") {
          const dataBuffer = await fs.readFile(filePath)
          const data = await pdfParse(dataBuffer)
          extractedText = data.text
        } else if (
          fileType === "application/vnd.openxmlformats-officedocument.wordprocessingml.document" ||
          fileType === "application/msword"
        ) {
          const buffer = await fs.readFile(filePath)
          const result = await mammoth.extractRawText({ buffer })
          extractedText = result.value
        } else if (fileType === "text/plain") {
          extractedText = await fs.readFile(filePath, "utf-8")
        } else if (fileType.startsWith("image/")) {
          extractedText = ""
        }
      } catch (e) {
        console.error("Erreur d'extraction:", e)
        extractedText = ""
      }

      // Générer un résumé (simulé ici)
      const summary = extractedText ? extractedText.substring(0, 150) + "... (Résumé simulé)" : "Résumé non disponible"

      // Supprimer le fichier après traitement
      await fs.unlink(filePath).catch(() => {})

      return resolve(
        NextResponse.json({
          summary,
          extractedText,
          fileName,
          fileType,
        })
      )
    })
  })
}
