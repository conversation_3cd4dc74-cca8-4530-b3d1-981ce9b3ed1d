import { NextResponse } from "next/server"
import { generateText } from "ai"
import * as mammoth from "mammoth"
import { extract } from "node-tika"
import { pdfToText } from "pdf-ts"
import { createWorker } from "tesseract.js"

import { openai } from "@ai-sdk/openai"
import { logger } from "@buildismart/lib"

export const runtime = "nodejs"
export const maxDuration = 300

async function extractTextFromImage(buffer: Buffer): Promise<string> {
  const worker = await createWorker()

  try {
    await worker.load()
    await worker.loadLanguage("fra+eng") // ✅ Correction ici
    await worker.initialize("fra+eng") // ✅ Correction ici

    const { data } = await worker.recognize(buffer)
    return data.text
  } finally {
    await worker.terminate()
  }
}

async function extractTextFromFile(file: File): Promise<string> {
  const buffer = Buffer.from(await file.arrayBuffer())
  const mimeType = file.type

  if (mimeType === "application/pdf") {
    return await pdfToText(buffer)
  }

  if (mimeType.includes("word") || file.name.endsWith(".docx") || file.name.endsWith(".doc")) {
    const result = await mammoth.extractRawText({ buffer })
    return result.value
  }

  if (mimeType.startsWith("image/")) {
    return await extractTextFromImage(buffer)
  }

  try {
    return await extract({ buffer })
  } catch {
    logger.warn("Fallback to plain text extraction")
    return file.text()
  }
}

export async function POST(req: Request) {
  try {
    const formData = await req.formData()
    const file = formData.get("file") as File

    if (!file) {
      throw new Error("Aucun fichier fourni")
    }

    const content = await extractTextFromFile(file)
    const truncatedContent = content.substring(0, 15000)

    const { text } = await generateText({
      model: openai("gpt-3.5-turbo-instruct"),
      prompt: `
You are an expert document analyst. Provide an extremely concise summary in exactly 2 lines, with no extra line breaks. Each line must be a maximum of 120 characters.
Document:
${truncatedContent}
      `.trim(),
    })

    return NextResponse.json({
      summary: text,
      extractedText: truncatedContent,
      fileName: file.name,
      fileType: file.type,
    })
  } catch (error: unknown) {
    logger.error("Erreur:", error)
    return NextResponse.json(
      { error: `Échec du traitement: ${error instanceof Error && error.message}` },
      { status: 500 }
    )
  }
}
