"use client"

import React from "react"
import { Bookmark, FilePlus, FolderPlus, FolderUp, MoreVertical, Pencil, Trash2 } from "lucide-react"

import { <PERSON><PERSON> } from "@nextui-org/button"
import { Dropdown, DropdownItem, DropdownMenu, DropdownSection, DropdownTrigger } from "@nextui-org/dropdown"

type FolderDropdownProps = {
  folderId: string
  folderTitle: string
  isSaved: boolean
  onDelete: (id: string) => void
  onToggleSave: (id: string) => void
  onStartRename: (id: string, currentTitle: string) => void
  onNewNestedFolder: (parentId: string) => void
  onNewFile: (parentId: string) => void
  onUploadFile: (parentId: string) => void
  onUploadFolder: (parentId: string) => void // Cette prop est bien déclarée ici
}

export default function FolderDropdown({
  folderId,
  folderTitle,
  isSaved,
  onDelete,
  onToggleSave,
  onStartRename,
  onNewNestedFolder,

  onUploadFile,
  onUploadFolder,
}: FolderDropdownProps) {
  return (
    <Dropdown placement="bottom-end">
      <DropdownTrigger>
        <Button
          isIconOnly
          variant="light"
          className="min-w-unit-0 absolute right-1 top-1/2 z-10 -translate-y-1/2 p-1"
          aria-label={`Options pour le dossier ${folderTitle}`}
        >
          <MoreVertical size={14} className="sm:size-4" color="white" />
        </Button>
      </DropdownTrigger>

      <DropdownMenu aria-label="Actions du dossier">
        <DropdownSection showDivider>
          <DropdownItem
            key="new-nested-folder"
            onPress={() => onNewNestedFolder(folderId)}
            startContent={<FolderPlus size={16} />}
          >
            Nouveau dossier
          </DropdownItem>
          <DropdownItem key="upload-file" onPress={() => onUploadFile(folderId)} startContent={<FilePlus size={16} />}>
            Télécharger un fichier
          </DropdownItem>
          {/* Nouvelle option pour télécharger un dossier dans ce dossier */}
          <DropdownItem
            key="upload-folder"
            onPress={() => onUploadFolder(folderId)}
            startContent={<FolderUp size={16} />}
          >
            Télécharger un dossier
          </DropdownItem>
          <DropdownItem
            key="rename"
            onPress={() => onStartRename(folderId, folderTitle)}
            startContent={<Pencil size={16} />}
          >
            Renommer
          </DropdownItem>
          <DropdownItem key="toggle-save" onPress={() => onToggleSave(folderId)} startContent={<Bookmark size={16} />}>
            {isSaved ? "Retirer des sauvegardes" : "Sauvegarder le dossier"}
          </DropdownItem>
        </DropdownSection>

        <DropdownSection>
          <DropdownItem
            key="delete"
            className="text-danger"
            color="danger"
            onPress={() => onDelete(folderId)}
            startContent={<Trash2 size={16} />}
          >
            Supprimer
          </DropdownItem>
        </DropdownSection>
      </DropdownMenu>
    </Dropdown>
  )
}
