"use client"
import React, { useRef } from "react"
//import Image from "next/image"

interface DocumentUploadTriggerProps {
  onUpload: (file: File) => void
  className?: string
}

export function DocumentUploadTrigger({ onUpload }: DocumentUploadTriggerProps) {
  const fileInputRef = useRef<HTMLInputElement | null>(null)

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      onUpload(file)
    }
    // Reset the input to allow uploading the same file again
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  return (
    <label className="flex w-full cursor-pointer justify-end">
      <input
        type="file"
        hidden
        onChange={handleFileChange}
        ref={fileInputRef}
        accept=".pdf,.doc,.docx,.txt,.png,.jpg,.jpeg,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
      />
      <div className="size-22 ml-8 mt-2 flex items-center justify-center rounded-full bg-secondary p-2 transition hover:opacity-80">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="22"
          height="22"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="lucide lucide-scan-text text-white"
        >
          <path d="M3 7V5a2 2 0 0 1 2-2h2" />
          <path d="M17 3h2a2 2 0 0 1 2 2v2" />
          <path d="M21 17v2a2 2 0 0 1-2 2h-2" />
          <path d="M7 21H5a2 2 0 0 1-2-2v-2" />
          <path d="M7 8h8" />
          <path d="M7 12h10" />
          <path d="M7 16h6" />
        </svg>
      </div>
    </label>
  )
}
