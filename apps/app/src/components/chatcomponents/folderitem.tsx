"use client"

import React, { useState } from "react"
import { ChevronDown, ChevronRight, FileText, Folder as FolderIcon } from "lucide-react"

import FolderDropdown from "@/components/chatcomponents/folderdropdown"

type FolderType = {
  id: string
  title: string
  createdAt: string
  parentId: string | null
  type: "folder" | "file"
  url?: string
}

type FolderItemProps = {
  folder: FolderType
  allFolders: FolderType[]
  activeId: string | null
  renamingId: string | null
  renameValue: string
  savedFolders: string[]
  setActiveId: (id: string | null) => void
  setRenamingId: (id: string | null) => void
  setRenameValue: (value: string) => void
  handleRenameFolder: (id: string) => void
  handleDeleteFolder: (id: string) => void
  handleToggleSave: (id: string) => void
  handleStartRename: (id: string, currentTitle: string) => void
  handleNewNestedFolder: (parentId: string) => void
  handleNewFile: (parentId: string) => void
  onUploadFile: (parentId: string) => void
  onUploadFolder: (parentId: string) => void
  onMobileClose?: () => void
}

export function FolderItem({
  folder,
  allFolders,
  activeId,
  renamingId,
  renameValue,
  savedFolders,
  setActiveId,
  setRenamingId,
  setRenameValue,
  handleRenameFolder,
  handleDeleteFolder,
  handleToggleSave,
  handleStartRename,
  handleNewNestedFolder,
  handleNewFile,
  onUploadFile,
  onUploadFolder,
  onMobileClose,
}: FolderItemProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const isParent = allFolders.some((item) => item.parentId === folder.id)
  const childItems = allFolders.filter((item) => item.parentId === folder.id)

  return (
    <div className="flex flex-col">
      <div className="group relative flex items-center">
        {folder.type === "folder" && isParent && (
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="mr-1 shrink-0 rounded-sm p-1 text-white hover:bg-white/10"
            aria-label={isExpanded ? "Collapse folder" : "Expand folder"}
          >
            {isExpanded ? <ChevronDown size={14} /> : <ChevronRight size={14} />}
          </button>
        )}
        {!(folder.type === "folder" && isParent) && <div className="w-[22px] shrink-0" />}
        <button
          className={`flex grow items-center gap-2 rounded p-2 text-left text-sm transition-colors duration-200 sm:text-base ${
            activeId === folder.id ? "bg-white/20" : "hover:bg-white/20"
          }`}
          onClick={() => {
            setActiveId(folder.id)
            if (folder.type === "folder" && isParent) setIsExpanded(!isExpanded)
            onMobileClose?.()
          }}
          onDoubleClick={() => {
            handleStartRename(folder.id, folder.title)
          }}
        >
          {folder.type === "folder" ? (
            <FolderIcon size={16} className="sm:size-[18px]" color="white" />
          ) : (
            <FileText size={16} className="sm:size-[18px]" color="white" />
          )}

          {renamingId === folder.id ? (
            <input
              autoFocus
              className="w-full rounded bg-white px-1 py-0.5 text-xs text-black sm:text-sm"
              value={renameValue}
              onChange={(e) => setRenameValue(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  e.currentTarget.blur()
                }
              }}
              onBlur={() => handleRenameFolder(folder.id)}
            />
          ) : (
            <span className="truncate text-sm text-white sm:text-base">{folder.title}</span>
          )}
        </button>
        {folder.type === "folder" && (
          <FolderDropdown
            folderId={folder.id}
            folderTitle={folder.title}
            isSaved={savedFolders.includes(folder.id)}
            onDelete={handleDeleteFolder}
            onToggleSave={handleToggleSave}
            onStartRename={handleStartRename}
            onNewNestedFolder={handleNewNestedFolder}
            onNewFile={handleNewFile}
            onUploadFile={onUploadFile}
            onUploadFolder={onUploadFolder}
          />
        )}
      </div>

      {folder.type === "folder" && isExpanded && childItems.length > 0 && (
        <div className="ml-6 border-l border-white/20">
          {childItems.map((childItem) => (
            <FolderItem
              key={childItem.id}
              folder={childItem}
              allFolders={allFolders}
              activeId={activeId}
              renamingId={renamingId}
              renameValue={renameValue}
              savedFolders={savedFolders}
              setActiveId={setActiveId}
              setRenamingId={setRenamingId}
              setRenameValue={setRenameValue}
              handleRenameFolder={handleRenameFolder}
              handleDeleteFolder={handleDeleteFolder}
              handleToggleSave={handleToggleSave}
              handleStartRename={handleStartRename}
              handleNewNestedFolder={handleNewNestedFolder}
              handleNewFile={handleNewFile}
              onUploadFile={onUploadFile}
              onUploadFolder={onUploadFolder}
              onMobileClose={onMobileClose}
            />
          ))}
        </div>
      )}
    </div>
  )
}
