import React from "react"
import Image from "next/image"
import { ArrowUp, X } from "lucide-react"

import { DocumentUploadTrigger } from "./documentupload"

interface ChatInputProps {
  input: string
  onInputChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void
  onSubmit: () => void
  onFileUpload: (file: File) => void
  uploadedFile: File | null
  onRemoveFile: () => void
  disabled?: boolean // Ajouter la prop disabled
  placeholder?: string // Ajouter la prop placeholder
}

function Spinner() {
  return <div className="size-5 animate-spin rounded-full border-b-2 border-white"></div>
}

export function ChatInput({
  input,
  onInputChange,
  onSubmit,
  onFileUpload,
  uploadedFile,
  onRemoveFile,
  disabled,
  placeholder,
}: ChatInputProps) {
  const [previewUrl, setPreviewUrl] = React.useState<string | null>(null)
  const [isSending, setIsSending] = React.useState(false) // État pour savoir si le message est en cours d'envoi

  React.useEffect(() => {
    if (uploadedFile) {
      const url = URL.createObjectURL(uploadedFile)
      setPreviewUrl(url)
      return () => URL.revokeObjectURL(url)
    } else {
      setPreviewUrl(null)
    }
  }, [uploadedFile])

  const handleSubmit = async () => {
    setIsSending(true)
    await onSubmit() // Soumettre le message
    setIsSending(false) // Réinitialiser l'état après l'envoi
  }

  return (
    <div className="-mb-4 w-full px-3 pb-4 pt-1">
      {" "}
      <form
        onSubmit={(e) => {
          e.preventDefault()
          handleSubmit() // Utiliser la fonction handleSubmit pour gérer l'envoi
        }}
        className="mx-auto w-full max-w-2xl"
      >
        <div className="relative">
          {previewUrl && (
            <div className="absolute bottom-full left-0 mb-2 flex w-full items-center gap-2 rounded-md border border-primary bg-background p-2">
              <div className="relative size-10 overflow-hidden rounded">
                {uploadedFile?.type.startsWith("image/") ? (
                  <Image src={previewUrl} alt="Document preview" fill className="object-cover" />
                ) : (
                  <div className="flex size-full items-center justify-center bg-black">
                    <span className="text-xs font-medium">{uploadedFile?.name.split(".").pop()?.toUpperCase()}</span>
                  </div>
                )}
              </div>
              <span className="flex-1 truncate text-sm">{uploadedFile?.name}</span>
              <button type="button" onClick={onRemoveFile} className="rounded-full p-1 text-black hover:bg-gray-200">
                <X size={16} />
              </button>
            </div>
          )}
          <textarea
            id="chat-input"
            rows={1}
            className="h-[108px] w-full resize-none rounded-md border border-primary py-3 pl-4 pr-24 text-black placeholder:text-black focus:outline-none"
            placeholder={placeholder || "Poser une question..."} // Utiliser le placeholder passé en prop ou une valeur par défaut
            value={input}
            onChange={onInputChange}
            onKeyDown={(e) => {
              if (e.key === "Enter" && !e.shiftKey) {
                e.preventDefault()
                handleSubmit() // Soumettre le message lorsque l'enter est pressé
              }
            }}
            disabled={disabled} // Appliquer la prop disabled à l'textarea
          />
          <div className="absolute right-3 top-1/2 flex -translate-y-1/2 gap-2">
            <DocumentUploadTrigger onUpload={onFileUpload} className="px-2" />
            <button
              type="submit"
              disabled={disabled || (!input.trim() && !uploadedFile) || isSending} // Désactiver le bouton si désactivé globalement, champ vide, fichier absent ou envoi en cours
              className={`mt-2 flex size-9 items-center justify-center rounded-full p-2 transition-colors ${
                isSending
                  ? "cursor-not-allowed bg-gray-400" // Fond gris et curseur désactivé pendant l'envoi
                  : "cursor-pointer bg-secondary" // Couleur normale avec hover si envoi non en cours
              }`}
              title="Envoyer le message"
            >
              {isSending ? (
                <Spinner /> // Affiche un spinner si l'envoi est en cours
              ) : (
                <ArrowUp size={28} className="text-white" /> // Affiche l'icône d'envoi sinon
              )}
            </button>
          </div>
        </div>
      </form>
    </div>
  )
}
