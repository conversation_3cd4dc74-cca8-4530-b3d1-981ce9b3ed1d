// components/Sidebar.tsx
"use client"

import React, { useEffect, useState } from "react"
import Image from "next/image"
import { useRouter } from "next/navigation"
import {
  Bookmark,
  ChevronDown,
  ChevronRight,
  FilePlus,
  Folder as FolderIcon,
  Menu,
  Plus,
  Search,
  X,
} from "lucide-react"

import { FolderItem } from "@/components/chatcomponents/folderitem"
import ModalCreateFolder from "@/components/chatcomponents/modalcreatefolder"
import { logger } from "@buildismart/lib"
import { Button, Input, Modal, ModalBody, ModalContent, ModalFooter, ModalHeader } from "@nextui-org/react"

type FolderType = {
  id: string
  title: string
  createdAt: string
  parentId: string | null
  type: "folder" | "file"
  url?: string
}

export function Sidebar() {
  const [collapsed, setCollapsed] = useState(false)
  const [mobileOpen, setMobileOpen] = useState(false)
  const [folders, setFolders] = useState<FolderType[]>([])
  const [activeId, setActiveId] = useState<string | null>(null)
  const [renamingId, setRenamingId] = useState<string | null>(null)
  const [renameValue, setRenameValue] = useState("")
  const [searchQuery, setSearchQuery] = useState("")
  const [showSavedOnly, setShowSavedOnly] = useState(false)
  const [savedFolders, setSavedFolders] = useState<string[]>([])
  const [showSearchInput, setShowSearchInput] = useState(false)
  const [showNotification, setShowNotification] = useState(false)
  const [notificationMessage, setNotificationMessage] = useState("")
  const [isModalCreateFolderOpen, setIsModalCreateFolderOpen] = useState(false)
  const [isNewNestedFolderModalOpen, setIsNewNestedFolderModalOpen] = useState(false)
  const [newNestedFolderParentId, setNewNestedFolderParentId] = useState<string | null>(null)
  const [newNestedFolderName, setNewNestedFolderName] = useState("")
  const [showNewActionsMenu, setShowNewActionsMenu] = useState(false)
  const [uploadTargetFolderId, setUploadTargetFolderId] = useState<string | null>(null)

  // SUPPRIMÉ: L'état `uploadFolderTargetId` n'est plus nécessaire.
  // const [uploadFolderTargetId, setUploadFolderTargetId] = useState<string | null>(null);

  const fileInputRef = React.useRef<HTMLInputElement>(null)
  const folderInputRef = React.useRef<HTMLInputElement>(null)

  // AJOUTÉ: Une référence pour stocker l'ID du dossier cible de manière synchrone
  const currentUploadFolderTargetId = React.useRef<string | null>(null)

  const router = useRouter()

  useEffect(() => {
    const saved = localStorage.getItem("folders")
    const savedFavs = localStorage.getItem("savedFolders")
    if (saved) setFolders(JSON.parse(saved) as FolderType[])
    if (savedFavs) setSavedFolders(JSON.parse(savedFavs) as string[])
  }, [])

  useEffect(() => {
    localStorage.setItem("folders", JSON.stringify(folders))
  }, [folders])

  useEffect(() => {
    localStorage.setItem("savedFolders", JSON.stringify(savedFolders))
  }, [savedFolders])

  useEffect(() => {
    if (showNotification) {
      const timer = setTimeout(() => {
        setShowNotification(false)
        setNotificationMessage("")
      }, 5000)
      return () => clearTimeout(timer)
    }
  }, [showNotification])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const sidebar = document.getElementById("mobile-sidebar")
      const toggle = document.getElementById("mobile-toggle")
      const newActionsMenu = document.getElementById("new-actions-menu")
      const newButton = document.getElementById("new-document-button")

      if (
        mobileOpen &&
        sidebar &&
        !sidebar.contains(event.target as Node) &&
        toggle &&
        !toggle.contains(event.target as Node)
      ) {
        setMobileOpen(false)
      }

      if (
        showNewActionsMenu &&
        newActionsMenu &&
        !newActionsMenu.contains(event.target as Node) &&
        newButton &&
        !newButton.contains(event.target as Node)
      ) {
        setShowNewActionsMenu(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [mobileOpen, showNewActionsMenu])

  const handleCreateFolder = (folderName: string, parentId: string | null = null) => {
    if (!folderName.trim()) return
    const newFolder: FolderType = {
      id: Date.now().toString(),
      title: folderName.trim(),
      createdAt: new Date().toISOString(),
      parentId: parentId,
      type: "folder",
    }
    setFolders([newFolder, ...folders])
    setIsModalCreateFolderOpen(false)
    setIsNewNestedFolderModalOpen(false)
    setNotificationMessage("Nouveau dossier créé avec succès !")
    setShowNotification(true)
  }

  const handleDeleteFolder = (id: string) => {
    const itemsToDelete: string[] = [id]
    let currentItems = [id]
    while (currentItems.length > 0) {
      const nextItems = folders.filter((f) => currentItems.includes(f.parentId as string)).map((f) => f.id)
      itemsToDelete.push(...nextItems)
      currentItems = nextItems
    }

    setFolders(folders.filter((f) => !itemsToDelete.includes(f.id)))
    setSavedFolders(savedFolders.filter((fid) => !itemsToDelete.includes(fid)))
    if (activeId === id) setActiveId(null)
    setNotificationMessage("Élément supprimé !")
    setShowNotification(true)
  }

  const handleRenameFolder = (id: string) => {
    const currentItem = folders.find((f) => f.id === id)
    if (currentItem && renameValue.trim() !== "" && renameValue.trim() !== currentItem.title) {
      setFolders(folders.map((f) => (f.id === id ? { ...f, title: renameValue.trim() } : f)))
      setNotificationMessage("Nom modifié avec succès !")
      setShowNotification(true)
    }
    setRenamingId(null)
  }

  const handleToggleSave = (id: string) => {
    let message = ""
    if (savedFolders.includes(id)) {
      setSavedFolders(savedFolders.filter((fid) => fid !== id))
      message = "Élément retiré des sauvegardes !"
    } else {
      setSavedFolders([...savedFolders, id])
      message = "Élément sauvegardé !"
    }
    setNotificationMessage(message)
    setShowNotification(true)
  }

  const handleStartRename = (id: string, currentTitle: string) => {
    setRenameValue(currentTitle)
    setRenamingId(id)
  }

  const handleNewNestedFolder = (parentId: string) => {
    logger.log(`Preparing to create a new nested folder under folder ID: ${parentId}`)
    setNewNestedFolderParentId(parentId)
    setNewNestedFolderName("")
    setIsNewNestedFolderModalOpen(true)
  }

  const handleCreateNewNestedFolder = () => {
    if (!newNestedFolderName.trim() || !newNestedFolderParentId) return

    handleCreateFolder(newNestedFolderName, newNestedFolderParentId)
    setNewNestedFolderParentId(null)
    setNewNestedFolderName("")
  }

  const handleNewFile = (parentId: string | null) => {
    // Cette fonction reste pour compatibilité si nécessaire, mais le bouton a été retiré.
    const newFile: FolderType = {
      id: Date.now().toString(),
      title: "Nouveau fichier",
      createdAt: new Date().toISOString(),
      parentId: parentId,
      type: "file",
      url: "#",
    }
    setFolders([newFile, ...folders])
    setNotificationMessage("Nouveau fichier créé !")
    setShowNotification(true)
  }

  const handleUploadFile = (file: File, parentId: string | null) => {
    if (!file) return

    const newFile: FolderType = {
      id: Date.now().toString() + Math.random().toString(36).substring(2, 9),
      title: file.name,
      createdAt: new Date().toISOString(),
      parentId: parentId,
      type: "file",
      url: URL.createObjectURL(file), // Permet de prévisualiser le fichier localement
    }
    setFolders((prevFolders) => [newFile, ...prevFolders])
    setNotificationMessage(`Fichier '${file.name}' ajouté avec succès !`)
    setShowNotification(true)
  }

  // Cette fonction est appelée par FolderItem/FolderDropdown
  const triggerFileUploadForFolder = (parentId: string) => {
    setUploadTargetFolderId(parentId)
    if (fileInputRef.current) {
      fileInputRef.current.click()
    }
  }

  const getRelativeDate = (dateStr: string) => {
    const created = new Date(dateStr)
    const today = new Date()
    const diff = Math.floor((today.getTime() - created.getTime()) / (1000 * 60 * 60 * 24))
    if (diff === 0) return "Aujourd'hui"
    if (diff === 1) return "Hier"
    if (diff < 7) return `Il y a ${diff} jours`
    return created.toLocaleDateString()
  }

  const displayedItems = folders
    .filter((f) => f.title.toLowerCase().includes(searchQuery.toLowerCase()))
    .filter((f) => !showSavedOnly || savedFolders.includes(f.id))

  const rootItems = displayedItems
    .filter((f) => f.parentId === null)
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())

  const groupedRootItems: Record<string, FolderType[]> = rootItems.reduce(
    (acc, folder) => {
      const label = getRelativeDate(folder.createdAt)
      if (!acc[label]) acc[label] = []
      acc[label].push(folder)
      return acc
    },
    {} as Record<string, FolderType[]>
  )

  const handleGlobalFolderUpload = async (
    event: React.ChangeEvent<HTMLInputElement>,
    targetParentId: string | null
  ) => {
    const files = event.target.files
    if (!files || files.length === 0) {
      setNotificationMessage("Aucun fichier sélectionné pour l'upload de dossier.")
      setShowNotification(true)
      return
    }

    const newItems: FolderType[] = []
    const uploadedRootFolderName = files[0].webkitRelativePath?.split("/")[0]
    let rootFolderIdForUpload: string | null = null

    if (uploadedRootFolderName) {
      rootFolderIdForUpload = Date.now().toString() + Math.random().toString(36).substring(2, 9)
      newItems.push({
        id: rootFolderIdForUpload,
        title: uploadedRootFolderName,
        createdAt: new Date().toISOString(),
        parentId: targetParentId, // Le dossier racine est placé sous ce parentId
        type: "folder",
      })
    } else {
      setNotificationMessage("Impossible de déterminer le nom du dossier racine. Veuillez sélectionner un dossier.")
      setShowNotification(true)
      return
    }

    const pathIdMap: { [path: string]: string } = {}
    pathIdMap[uploadedRootFolderName] = rootFolderIdForUpload

    for (let i = 0; i < files.length; i++) {
      const file = files[i]
      const relativePath = file.webkitRelativePath

      if (!relativePath) continue

      const pathParts = relativePath.split("/")
      let currentParentId: string | null = rootFolderIdForUpload
      let currentFullPath = uploadedRootFolderName

      for (let j = 1; j < pathParts.length; j++) {
        const part = pathParts[j]
        currentFullPath = `${currentFullPath}/${part}`

        if (j === pathParts.length - 1) {
          // C'est un fichier
          newItems.push({
            id: Date.now().toString() + Math.random().toString(36).substring(2, 9),
            title: file.name,
            createdAt: new Date().toISOString(),
            parentId: currentParentId,
            type: "file",
            url: URL.createObjectURL(file),
          })
        } else {
          // C'est un dossier (ou un sous-dossier)
          let folderId = pathIdMap[currentFullPath]

          if (!folderId) {
            folderId = Date.now().toString() + Math.random().toString(36).substring(2, 9)
            newItems.push({
              id: folderId,
              title: part,
              createdAt: new Date().toISOString(),
              parentId: currentParentId,
              type: "folder",
            })
            pathIdMap[currentFullPath] = folderId
          }
          currentParentId = folderId
        }
      }
    }

    setFolders((prevFolders) => [...newItems, ...prevFolders])
    setNotificationMessage(`Dossier '${uploadedRootFolderName}' et son contenu ajoutés !`)
    setShowNotification(true)
    setShowNewActionsMenu(false)
  }

  // MODIFIÉ: triggerFolderUploadForFolder utilise maintenant useRef
  const triggerFolderUploadForFolder = (parentId: string) => {
    currentUploadFolderTargetId.current = parentId // Stocke l'ID dans le ref
    if (folderInputRef.current) {
      folderInputRef.current.click() // Ouvre la fenêtre de sélection de fichier/dossier
    }
  }

  return (
    <>
      <button
        id="mobile-toggle"
        onClick={() => setMobileOpen(!mobileOpen)}
        className="fixed left-4 top-4 z-50 rounded-md bg-primary p-2 text-white shadow-lg md:hidden"
      >
        {mobileOpen ? <X size={20} /> : <Menu size={20} />}
      </button>

      {mobileOpen && <div className="fixed inset-0 z-40 bg-black/50 md:hidden" />}

      <aside
        id="mobile-sidebar"
        className={`fixed z-50 flex h-screen flex-col bg-primary transition-all duration-300 md:relative md:z-auto ${
          mobileOpen ? "translate-x-0" : "-translate-x-full md:translate-x-0"
        } ${collapsed ? "w-16 md:w-16" : "w-4/5 sm:w-2/3 md:w-1/5"} `}
      >
        <div className="flex items-center justify-between p-2 sm:px-4">
          {!collapsed ? (
            <Image
              src="/l.png"
              alt="Logo"
              width={230}
              height={230}
              className="ml-2 block h-auto w-32 sm:ml-4 sm:w-48 md:w-[230px]"
            />
          ) : (
            <div className="w-8"></div>
          )}

          <button onClick={() => setCollapsed(!collapsed)} className="hidden shrink-0 md:block">
            <Image src="/col.png" alt="Toggle sidebar" width={60} height={60} className="size-6 sm:size-8" />
          </button>
        </div>

        <div className="mt-2 flex flex-1 flex-col gap-2 overflow-y-auto p-2 px-4 sm:gap-4 sm:p-4 sm:px-8">
          {!collapsed ? (
            <>
              <div className="relative">
                <button
                  id="new-document-button"
                  onClick={() => setShowNewActionsMenu(!showNewActionsMenu)}
                  className="flex w-full items-center gap-2 rounded bg-primary px-3 py-2 text-sm text-white transition-colors duration-200 hover:bg-white/20 sm:px-5 sm:text-lg"
                >
                  <Plus size={16} className="sm:size-5" color="white" />
                  <span className="hidden sm:inline">Nouveau</span>
                  <span className="sm:hidden">Nouveau</span>
                  {showNewActionsMenu ? (
                    <ChevronDown size={16} className="ml-auto" />
                  ) : (
                    <ChevronRight size={16} className="ml-auto" />
                  )}
                </button>

                {showNewActionsMenu && (
                  <div
                    id="new-actions-menu"
                    className="absolute inset-x-0 top-full z-10 mt-1 rounded bg-white py-1 shadow-lg"
                  >
                    <button
                      onClick={() => {
                        setIsModalCreateFolderOpen(true)
                        setShowNewActionsMenu(false)
                      }}
                      className="flex w-full items-center gap-2 px-4 py-2 text-sm text-black hover:bg-gray-100"
                    >
                      <FolderIcon size={16} /> Nouveau dossier
                    </button>
                    <label
                      htmlFor="upload-folder-input"
                      className="flex w-full cursor-pointer items-center gap-2 px-4 py-2 text-sm text-black hover:bg-gray-100"
                    >
                      <FolderIcon size={16} /> Télécharger un dossier
                      <input
                        id="upload-folder-input"
                        type="file"
                        // @ts-expect-error: webkitdirectory est une propriété non-standard mais couramment utilisée pour les dossiers
                        webkitdirectory="true"
                        directory="" // Ancien attribut pour la compatibilité
                        multiple
                        // MODIFIÉ: Appel de handleGlobalFolderUpload pour l'upload de dossier global
                        onChange={(e) => handleGlobalFolderUpload(e, null)}
                        className="hidden"
                      />
                    </label>
                    <label
                      htmlFor="upload-file-input-global"
                      className="flex w-full cursor-pointer items-center gap-2 px-4 py-2 text-sm text-black hover:bg-gray-100"
                    >
                      <FilePlus size={16} /> Télécharger un fichier
                      <input
                        id="upload-file-input-global"
                        type="file"
                        onChange={(e) => {
                          if (e.target.files && e.target.files.length > 0) {
                            handleUploadFile(e.target.files[0], null)
                            setShowNewActionsMenu(false)
                            e.target.value = "" // Réinitialiser l'input pour permettre le re-upload du même fichier
                          }
                        }}
                        className="hidden"
                      />
                    </label>
                    {/* Le bouton "Nouveau fichier (virtuel)" a été supprimé ici */}
                  </div>
                )}
              </div>

              <div>
                {showSearchInput ? (
                  <input
                    type="text"
                    autoFocus
                    className="w-full rounded bg-white px-2 py-1 text-xs text-black sm:text-sm"
                    placeholder="Rechercher un dossier..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    onBlur={() => {
                      if (!searchQuery) setShowSearchInput(false)
                    }}
                  />
                ) : (
                  <button
                    type="button"
                    onClick={() => setShowSearchInput(true)}
                    className="flex w-full items-center gap-2 rounded bg-primary px-3 py-2 text-sm text-white transition-colors duration-200 hover:bg-white/20 sm:px-5 sm:text-lg"
                  >
                    <Search size={16} className="sm:size-5" color="white" />
                    Rechercher
                  </button>
                )}
              </div>

              <button
                type="button"
                onClick={() => setShowSavedOnly(!showSavedOnly)}
                className={`flex items-center gap-2 rounded px-3 py-2 text-sm text-white transition-colors duration-200 sm:px-5 sm:text-lg ${
                  showSavedOnly ? "bg-white/20" : "bg-primary hover:bg-white/20"
                }`}
              >
                <Bookmark size={16} className="sm:size-5" color="white" />
                Sauvegarde
              </button>

              {showNotification && (
                <div className="fixed right-4 top-16 z-50 rounded-md bg-primary px-3 py-2 text-sm text-white shadow-lg sm:top-4 sm:px-4 sm:text-base">
                  {notificationMessage}
                </div>
              )}

              <ModalCreateFolder
                isOpen={isModalCreateFolderOpen}
                onClose={() => setIsModalCreateFolderOpen(false)}
                onCreate={(name) => handleCreateFolder(name, null)}
              />

              <Modal
                isOpen={isNewNestedFolderModalOpen}
                onOpenChange={setIsNewNestedFolderModalOpen}
                backdrop="opaque"
                placement="center"
              >
                <ModalContent>
                  {(onClose) => (
                    <>
                      <ModalHeader className="flex flex-col gap-1">
                        Nouveau dossier sous &quot;
                        {folders.find((f) => f.id === newNestedFolderParentId)?.title || "un dossier"}&quot;
                      </ModalHeader>
                      <ModalBody>
                        <Input
                          type="text"
                          label="Nom du dossier"
                          placeholder="Entrez le nom du dossier"
                          value={newNestedFolderName}
                          onChange={(e) => setNewNestedFolderName(e.target.value)}
                          isRequired
                          autoFocus
                          onKeyPress={(e) => {
                            if (e.key === "Enter") {
                              handleCreateNewNestedFolder()
                              onClose()
                            }
                          }}
                        />
                      </ModalBody>
                      <ModalFooter>
                        <Button
                          variant="light"
                          onPress={() => {
                            setNewNestedFolderName("")
                            setNewNestedFolderParentId(null)
                            onClose()
                          }}
                        >
                          Annuler
                        </Button>
                        <Button
                          color="primary"
                          onPress={() => {
                            handleCreateNewNestedFolder()
                            onClose()
                          }}
                        >
                          Créer
                        </Button>
                      </ModalFooter>
                    </>
                  )}
                </ModalContent>
              </Modal>

              {/* Input caché pour le téléchargement de fichiers dans un dossier existant */}
              <input
                type="file"
                ref={fileInputRef}
                onChange={(e) => {
                  if (e.target.files && e.target.files.length > 0 && uploadTargetFolderId) {
                    handleUploadFile(e.target.files[0], uploadTargetFolderId)
                    setUploadTargetFolderId(null)
                    e.target.value = ""
                  }
                }}
                className="hidden"
              />
              {/* Input caché pour le téléchargement de dossiers */}
              <input
                type="file"
                ref={folderInputRef}
                // @ts-expect-error: webkitdirectory est une propriété non-standard
                webkitdirectory="true"
                directory=""
                multiple
                onChange={(e) => {
                  if (e.target.files && e.target.files.length > 0) {
                    // Utilisez la valeur du ref ici
                    handleGlobalFolderUpload(e, currentUploadFolderTargetId.current)
                    // Réinitialisez le ref après utilisation
                    currentUploadFolderTargetId.current = null // Très important de nettoyer
                    e.target.value = ""
                  }
                }}
                className="hidden"
              />

              {Object.entries(groupedRootItems).map(([label, items]) => (
                <div key={label}>
                  <div className="mt-4 text-xs text-gray-200 sm:text-sm">{label}</div>
                  <div className="flex flex-col gap-1 sm:gap-2">
                    {items.map((f) => (
                      <FolderItem
                        key={f.id}
                        folder={f}
                        allFolders={displayedItems}
                        activeId={activeId}
                        renamingId={renamingId}
                        renameValue={renameValue}
                        savedFolders={savedFolders}
                        setActiveId={setActiveId}
                        setRenamingId={setRenamingId}
                        setRenameValue={setRenameValue}
                        handleRenameFolder={handleRenameFolder}
                        handleDeleteFolder={handleDeleteFolder}
                        handleToggleSave={handleToggleSave}
                        handleStartRename={handleStartRename}
                        handleNewNestedFolder={handleNewNestedFolder}
                        handleNewFile={handleNewFile}
                        onUploadFile={triggerFileUploadForFolder}
                        onUploadFolder={triggerFolderUploadForFolder}
                        onMobileClose={() => setMobileOpen(false)}
                      />
                    ))}
                  </div>
                </div>
              ))}
            </>
          ) : (
            <button
              onClick={() => setCollapsed(false)}
              className="flex items-center justify-center rounded p-2 text-white transition-colors duration-200 hover:bg-white/20"
            >
              <Search size={16} className="sm:size-[18px]" />
            </button>
          )}
        </div>

        {!collapsed && (
          <div className="h-32 px-3 sm:h-40 sm:px-5">
            <div className="relative m-2 overflow-hidden rounded-lg bg-gray100/70 p-3 text-xs text-black sm:m-4 sm:p-4 sm:text-sm">
              <div className="opacity-8 pointer-events-none absolute inset-0 mt-12 bg-[url('/Vector.png')] bg-cover bg-bottom" />

              <div className="relative z-10 flex size-full flex-col justify-center">
                <p className="mb-2">
                  Accédez à l&rsquo;analyse illimitée de vos appels d&rsquo;offres, aux exports intelligents, et à bien
                  plus encore.
                </p>
                <div className="flex justify-center">
                  <button
                    onClick={() => router.push("/")}
                    className="rounded bg-secondary px-2 py-1 text-xs text-black transition-colors hover:bg-couleur-tertiaire sm:px-3 sm:text-sm"
                  >
                    <span className="hidden sm:inline">Passer au plan Premium</span>
                    <span className="sm:hidden">Premium</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </aside>
    </>
  )
}
