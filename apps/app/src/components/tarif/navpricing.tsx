"use client"

import React from "react"
import Image from "next/image"
import Link from "next/link"
import { useParams } from "next/navigation"

import { But<PERSON> } from "@nextui-org/button"

type NavPricingProps = {
  className?: string
}

const NavPricing: React.FC<NavPricingProps> = ({ className = "" }) => {
  const params = useParams()
  const lang = (params?.lang as string) || "fr"

  return (
    <nav className={`flex w-full items-center justify-between px-2 py-4 sm:px-4 md:px-8 lg:px-10 ${className}`}>
      <div className="mr-2 shrink-0 sm:mr-4 md:mr-0">
        {" "}
        <Link href="/">
          <Image src="/logoBlue.png" alt="BUILDISMART" width={200} height={40} className="h-6 w-auto sm:h-8 md:h-12" />
        </Link>
      </div>

      <div className="grow"></div>

      <div className="shrink-0">
        <div className="flex items-center gap-1 sm:gap-2 md:gap-4">
          <Button
            as={Link}
            href={`/${lang}/sign-up`}
            variant="bordered"
            className="rounded-full border border-primary px-2 py-0.5 text-[0.6rem] text-primary transition-colors hover:bg-primary/10 sm:px-2.5 sm:py-1 sm:text-[0.65rem] md:px-8 md:py-3 md:text-base"
          >
            S&rsquo;inscrire
          </Button>

          <Button
            as={Link}
            href={`/${lang}/sign-in`}
            variant="solid"
            className="rounded-full bg-primary px-2 py-0.5 text-[0.6rem] text-white transition-colors hover:bg-primary/90 sm:px-2.5 sm:py-1 sm:text-[0.65rem] md:px-8 md:py-3 md:text-base"
          >
            Se connecter
          </Button>
        </div>
      </div>
    </nav>
  )
}

export default NavPricing
