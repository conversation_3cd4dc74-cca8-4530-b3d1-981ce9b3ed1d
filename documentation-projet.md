## Table des Matières

1. [Vue d'ensemble du projet](#vue-densemble-du-projet)
2. [Architecture générale](#architecture-générale)
3. [Structure des dossiers](#structure-des-dossiers)
4. [Configuration de l'environnement](#configuration-de-lenvironnement)
5. [tRPC - Communication Client/Serveur](#trpc---communication-clientserveur)
6. [Authentification avec NextAuth](#authentification-avec-nextauth)
7. [Base de données avec Prisma](#base-de-données-avec-prisma)
8. [Sécurité du projet](#sécurité-du-projet)
9. [Gestion des utilisateurs](#gestion-des-utilisateurs)
10. [Services externes](#services-externes)
11. [Développement et bonnes pratiques](#développement-et-bonnes-pratiques)
12. [Déploiement](#déploiement)

---

## Vue d'ensemble du projet

Le boilerpate sert de base pour une application web moderne construite avec Next.js 14, utilisant une architecture monorepo avec Turbo. Le projet est organisé en plusieurs applications et packages partagés.

### Technologies principales

- **Frontend**: Next.js 14 avec App Router, React 18, TypeScript
- **Backend**: tRPC pour l'API, NextAuth pour l'authentification
- **Base de données**: PostgreSQL avec Prisma ORM
- **Cache**: Redis pour les sessions et le cache
- **Stockage**: AWS S3 compatible pour les fichiers
- **UI**: NextUI + Tailwind CSS + Lucide (icônes)
- **Monorepo**: Turbo + npm workspaces

### Applications

1. **`apps/app`** - Application principale Next.js
2. **`apps/cron`** - Tâches cron et jobs en arrière-plan

### Packages partagés

1. **`packages/lib`** - Utilitaires et fonctions partagées
2. **`packages/transactional`** - Templates d'emails transactionnels
3. **`packages/emails`** - Gestion des emails
4. **`packages/configs`** - Configurations ESLint, Prettier, TypeScript
5. **`packages/scripts`** - Scripts d'initialisation et utilitaires

---

## Architecture générale

Le projet suit une architecture en couches avec séparation claire des responsabilités :

```
┌─────────────────────────────────────┐
│           Frontend (React)          │
├─────────────────────────────────────┤
│         tRPC Client Layer           │
├─────────────────────────────────────┤
│         tRPC Server Layer           │
├─────────────────────────────────────┤
│        Business Logic Layer        │
├─────────────────────────────────────┤
│         Data Access Layer          │
│            (Prisma ORM)             │
├─────────────────────────────────────┤
│         Database (PostgreSQL)       │
└─────────────────────────────────────┘
```

### Flux de données

1. **Client** → tRPC Client → HTTP Request
2. **Serveur** → tRPC Router → Middleware → Procédure
3. **Procédure** → Business Logic → Prisma → Database
4. **Response** → tRPC → Client

---

## Structure des dossiers

### Application principale (`apps/app/src/`)

```
src/
├── api/                    # Routeurs tRPC
│   ├── auth/              # Authentification
│   ├── me/                # Gestion utilisateur
│   └── upload/            # Upload de fichiers
├── app/                   # Pages Next.js (App Router)
│   ├── [lang]/           # Routes internationalisées
│   └── api/              # API routes Next.js
├── components/           # Composants React
│   ├── auth/            # Composants d'authentification
│   ├── ui/              # Composants UI réutilisables
│   └── ...
├── lib/                 # Utilitaires et configurations
│   ├── auth/           # Configuration NextAuth
│   ├── trpc/           # Configuration tRPC
│   ├── server/         # Utilitaires serveur
│   └── utils/          # Fonctions utilitaires
├── hooks/              # Hooks React personnalisés
├── schemas/            # Schémas de validation Zod
├── types/              # Types TypeScript
├── constants/          # Constantes de l'application
└── langs/              # Fichiers de traduction
```

### Packages

```
packages/
├── lib/                # Bibliothèque partagée
├── transactional/      # Templates d'emails
├── emails/             # Gestion des emails
├── configs/            # Configurations partagées
└── scripts/            # Scripts d'initialisation
```

---

## Configuration de l'environnement

Le projet est conçu pour être exécuté dans un environnement **Dockerisé** afin d'assurer une cohérence entre les machines de développement. Cela permet d'éviter les problèmes de compatibilité liés aux versions de dépendances, de système ou d'outils.

### Prérequis

- **Docker Desktop** installé et fonctionnel
- **VS Code** avec l'extension **Dev Containers**
- **Git**

### Installation de Docker

1. Rendez-vous sur [https://www.docker.com/products/docker-desktop](https://www.docker.com/products/docker-desktop)
2. Téléchargez Docker Desktop pour votre système (Windows, macOS ou Linux)
3. Suivez les instructions d'installation officielles
4. Vérifiez que Docker est bien installé :

```bash
docker --version
```

> **Sous Windows**, Docker Desktop nécessite que WSL2 soit activé (Windows Subsystem for Linux).

### Installation de l'extension Dev Containers

Vous pouvez vous rendre sur ce [lien vers l'extension](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.remote-containers) et effectuer l'installation ou suivre les étapes ci-après:

1. Ouvrez **VS Code**
2. Accédez à la Marketplace des extensions (`Ctrl+Shift+X`)
3. Recherchez : `Dev Containers`
4. Installez l’extension officielle de Microsoft

> Cette extension permet à VS Code d’ouvrir directement le projet dans un conteneur Docker prêt à l’emploi.

---

### Cas de limitation de performances

Si votre machine ne parvient **pas à exécuter Docker de manière fluide** (RAM insuffisante, performances instables, etc.), il est **obligatoire** d'exécuter le projet directement sur un environnement **Linux**, selon l'une des deux options suivantes :

- Via **WSL2** (Ubuntu recommandé) sur Windows
- Via une **installation Linux native**

L’environnement Linux est nécessaire pour garantir la compatibilité et le bon fonctionnement du projet.

#### Configuration de l’environnement Linux

Une fois sur un environnement Linux, vous devez installer et configurer les services suivants localement :

##### 1. Installation de Redis

```bash
sudo apt update
sudo apt install redis-server
```

Démarrez et activez Redis au démarrage :

```bash
# Système classique (Linux natif)
sudo systemctl enable redis-server
sudo systemctl start redis-server

# Ou sur wsl
sudo service redis-server start
```

##### 2. Installation de PostgreSQL

```bash
sudo apt install postgresql postgresql-contrib
```

Démarrez le service PostgreSQL:

```bash
# Système classique (Linux natif)
sudo systemctl enable postgresql
sudo systemctl start postgresql

# Ou sur WSL
sudo service postgresql start
```

##### 3. Création d’un utilisateur PostgreSQL

Créez un utilisateur PostgreSQL :

```bash
sudo -u postgres createuser --interactive
```

Répondez aux questions pour accorder les droits souhaités à l'utilisateur.

##### 4. Création de la base de données

Une fois l'utilisateur créé, utilisez la commande suivante pour créer une base de données :

```bash
createdb -U votre_user nom_de_la_bdd
```

Veillez à adapter les permissions au besoin via `psql`.

---

### Variables d'environnement requises

Le projet utilise `@t3-oss/env-nextjs` pour la validation des variables d'environnement, la configuration est visible dans le fichier `apps/app/src/lib/env.ts`

#### Variables serveur

```bash
# Base de données
DATABASE_PRISMA_URL="postgresql://user:password@localhost:5432/db"
DATABASE_URL_NON_POOLING="postgresql://user:password@localhost:5432/db"

# Authentification
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="http://localhost:3000"
PASSWORD_HASHER_SECRET="your-hasher-secret"

# GitHub OAuth (optionnel)
GITHUB_CLIENT_ID="your-github-client-id"
GITHUB_CLIENT_SECRET="your-github-client-secret"

# Admin par défaut
AUTH_ADMIN_EMAIL="<EMAIL>"
AUTH_ADMIN_PASSWORD="SecurePassword123!"

# Redis
REDIS_URL="redis://localhost:6379"
# OU
REDIS_HOST="localhost"
REDIS_PORT="6379"
REDIS_USERNAME=""
REDIS_PASSWORD=""
REDIS_USE_TLS="false"

# Email (optionnel)
SMTP_HOST="smtp.example.com"
SMTP_PORT="587"
SMTP_USERNAME="your-smtp-username"
SMTP_PASSWORD="your-smtp-password"
SMTP_FROM_NAME="Your App Name"
SMTP_FROM_EMAIL="<EMAIL>"
SUPPORT_EMAIL="<EMAIL>"

# S3 (optionnel)
S3_REGION="us-east-1"
S3_ACCESS_KEY_ID="your-access-key"
S3_SECRET_ACCESS_KEY="your-secret-key"
ENABLE_S3_SERVICE="false"

# Configuration
ENV="development"
DISABLE_REGISTRATION="false"
OPENAI_API_KEY="your-openai-key" # optionnel
```

#### Variables client

```bash
# URL de base
NEXT_PUBLIC_BASE_URL="http://localhost:3000"

# Mode démo
NEXT_PUBLIC_IS_DEMO="false"
NEXT_PUBLIC_DEMO_EMAIL="<EMAIL>"
NEXT_PUBLIC_DEMO_PASSWORD="DemoPassword123!"

# S3 public
NEXT_PUBLIC_S3_ENDPOINT="s3.amazonaws.com"
NEXT_PUBLIC_S3_BUCKET_NAME="your-bucket"

# Services
NEXT_PUBLIC_ENABLE_MAILING_SERVICE="false"
```

### Obtention du projet

1. Cloner le dépôt GitHub:

```bash
git clone [lien ssh du repo du projet]
```

2. Créer une branche en local:

```bash
git checkout -b [nom de votre branche]
```

3. Tirer de la branche dev pour être sûr d'avoir les derniers changements:

```bash
git pull origin dev --rebase
```

### Initialisation du projet

1. **Copier le fichier d'environnement** :

```bash
cp apps/app/.env.example apps/app/.env
```

2. **Installer les dépendances** :

```bash
npm install
```

3. **Initialiser le projet** :

```bash
npm run init
```

Cette commande va :

- Configurer la base de données
- Exécuter les migrations Prisma
- Créer l'utilisateur admin
- Configurer les hooks Git

### Démarrage du projet

1. Changer de répertoire de travail:

```bash
cd apps/app
```

2. Lancer le serveur de développement:

```bash
npm run dev
```

---

## tRPC - Communication Client/Serveur

tRPC permet une communication type-safe entre le client et le serveur sans avoir besoin de générer de code.

### Architecture tRPC

#### 1. Configuration serveur (`src/lib/server/trpc.ts`)

```typescript
import { initTRPC } from "@trpc/server";
import superjson from "superjson";

// Initialisation de tRPC avec le contexte
const t = initTRPC.context<Context>().create({
  transformer: superjson, // Sérialisation des dates, etc.
  errorFormatter(opts) {
    // Formatage des erreurs Zod
    const { shape, error } = opts;
    return {
      ...shape,
      data: {
        ...shape.data,
        zodError:
          error.code === "BAD_REQUEST" && error.cause instanceof ZodError
            ? error.cause.flatten()
            : null,
      },
    };
  },
});

// Export des helpers
export const router = t.router;
export const middleware = t.middleware;
export const publicProcedure = t.procedure.use(hasRateLimit);
export const authenticatedProcedure = publicProcedure
  .use(isAuthenticated)
  .use(hasVerifiedEmail);
```

#### 2. Middlewares

**Rate Limiting** :

```typescript
const hasRateLimit = middleware(async (opts) => {
  if (opts.ctx.req) {
    const { headers } = await apiRateLimiter(opts.ctx.req);
    return opts.next({
      ctx: { Headers: headers },
    });
  }
  return opts.next();
});
```

**Authentification** :

```typescript
const isAuthenticated = middleware(async (opts) => {
  const { session } = await getAuthApi();

  if (!session) {
    await ApiError("unauthorized", "UNAUTHORIZED");
  }

  return opts.next({
    ctx: { ...opts.ctx, session },
  });
});
```

**Vérification email** :

```typescript
const hasVerifiedEmail = middleware(async (opts) => {
  const { ctx } = opts;
  const session = ctx.session as Session & { user: User };

  if (
    !session ||
    (!session.user.emailVerified && env.NEXT_PUBLIC_ENABLE_MAILING_SERVICE)
  ) {
    await ApiError("emailNotVerified", "UNAUTHORIZED", {
      redirect: false,
    });
  }

  return opts.next();
});
```

#### 3. Définition des routeurs (`src/api/_app.ts`)

```typescript
import { router } from "../lib/server/trpc";
import { authRouter } from "./auth/_router";
import { meRouter } from "./me/_router";
import { uploadRouter } from "./upload/_router";

export const appRouter = router({
  auth: authRouter, // Authentification
  me: meRouter, // Gestion utilisateur
  upload: uploadRouter, // Upload de fichiers
});

export type AppRouter = typeof appRouter;
```

#### 4. Exemple de routeur (`src/api/auth/_router.ts`)

```typescript
import {
  authenticatedProcedure,
  publicProcedure,
  router,
} from "@/lib/server/trpc";
import { signUpSchema, signUpResponseSchema } from "./schemas";
import { register } from "./mutations";

export const authRouter = router({
  // Procédure publique avec validation d'entrée et sortie
  register: publicProcedure
    .input(signUpSchema()) // Validation Zod de l'input
    .output(signUpResponseSchema()) // Validation Zod de l'output
    .mutation(register), // Fonction de mutation

  // Procédure authentifiée
  generateTotpSecret: authenticatedProcedure
    .output(generateTotpSecretResponseSchema())
    .mutation(generateTotpSecret),
});
```

#### 5. Configuration client (`src/lib/trpc/client.ts`)

```typescript
import { createTRPCReact, httpBatchLink, loggerLink } from "@trpc/react-query";
import SuperJSON from "superjson";
import { type AppRouter } from "@/api/_app";

export const trpc = createTRPCReact<AppRouter>({});

export const trpcClient = trpc.createClient({
  links: [
    // Logs en développement
    loggerLink({
      enabled: (opts) =>
        process.env.NODE_ENV === "development" ||
        (opts.direction === "down" && opts.result instanceof Error),
    }),
    // Requêtes HTTP groupées
    httpBatchLink({
      url: `${getBaseUrl()}/api/trpc`,
      transformer: SuperJSON,
    }),
  ],
});
```

#### 6. Provider React (`src/lib/trpc/provider.tsx`)

```typescript
"use client"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { trpc, trpcClient } from "./client"

export default function TrpcProvider({ children, dictionary }) {
  const [queryClient] = useState(() => new QueryClient({
    queryCache: new QueryCache({
      onError: (error, query) => {
        // Gestion globale des erreurs
        if (error instanceof TRPCClientError) {
          handleQueryError(error, dictionary, router)
        }
      },
    }),
  }))

  return (
    <trpc.Provider client={trpcClient} queryClient={queryClient}>
      <QueryClientProvider client={queryClient}>
        {children}
      </QueryClientProvider>
    </trpc.Provider>
  )
}
```

### Utilisation côté client

#### 1. Dans un composant React

```typescript
"use client"
import { trpc } from "@/lib/trpc/client"

export default function UserProfile() {
  // Query (lecture)
  const { data: user, isLoading } = trpc.me.getAccount.useQuery()

  // Mutation (écriture)
  const updateUser = trpc.me.updateUser.useMutation({
    onSuccess: () => {
      // Invalidation du cache pour refetch
      trpc.me.getAccount.invalidate()
    }
  })

  const handleUpdate = (data) => {
    updateUser.mutate(data)
  }

  if (isLoading) return <div>Chargement...</div>

  return (
    <div>
      <h1>{user?.username}</h1>
      <button onClick={() => handleUpdate({ username: "nouveau-nom" })}>
        Mettre à jour
      </button>
    </div>
  )
}
```

#### 2. Côté serveur (Server Components)

```typescript
import { serverTrpc } from "@/lib/trpc/server"

export default async function ServerUserProfile() {
  // Appel direct côté serveur
  const user = await serverTrpc.me.getAccount()

  return (
    <div>
      <h1>{user.username}</h1>
      <p>{user.email}</p>
    </div>
  )
}
```

### Avantages de tRPC

1. **Type Safety** : Types partagés automatiquement entre client et serveur
2. **Pas de génération de code** : Types inférés directement du code TypeScript
3. **Validation automatique** : Avec Zod pour l'input/output
4. **Gestion d'erreurs** : Erreurs typées et formatées
5. **Performance** : Requêtes groupées et cache intelligent
6. **DX** : Autocomplétion et refactoring sûr

### Créer un routeur tRPC complet - Guide pour débutants

Cette section explique comment créer un routeur tRPC de A à Z avec des exemples concrets.

#### 1. Comprendre la structure d'un routeur tRPC

Un routeur tRPC est composé de :

- **Queries** : Pour lire des données (GET)
- **Mutations** : Pour modifier des données (POST/PUT/DELETE)
- **Schémas de validation** : Pour valider les entrées et sorties
- **Middlewares** : Pour la sécurité et la logique transversale

#### 2. Exemple complet : Routeur de gestion des articles

Créons un routeur pour gérer des articles de blog étape par étape.

##### Étape 1 : Définir le modèle Prisma

```prisma
// prisma/schema.prisma
model Article {
  id          String   @id @default(cuid())
  title       String
  content     String   @db.Text
  published   Boolean  @default(false)
  authorId    String
  author      User     @relation(fields: [authorId], references: [id])
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("articles")
}

// Ajouter la relation dans le modèle User
model User {
  // ... autres champs
  articles    Article[]
}
```

##### Étape 2 : Créer les schémas de validation

```typescript
// src/api/articles/schemas.ts
import { z } from "zod";

// Schéma pour créer un article
export const createArticleSchema = () =>
  z.object({
    title: z
      .string()
      .min(1, "Le titre est requis")
      .max(200, "Le titre ne peut pas dépasser 200 caractères"),
    content: z
      .string()
      .min(1, "Le contenu est requis")
      .max(10000, "Le contenu ne peut pas dépasser 10000 caractères"),
    published: z.boolean().optional().default(false),
  });

// Schéma pour mettre à jour un article
export const updateArticleSchema = () =>
  z.object({
    id: z.string().cuid("ID d'article invalide"),
    title: z
      .string()
      .min(1, "Le titre est requis")
      .max(200, "Le titre ne peut pas dépasser 200 caractères")
      .optional(),
    content: z
      .string()
      .min(1, "Le contenu est requis")
      .max(10000, "Le contenu ne peut pas dépasser 10000 caractères")
      .optional(),
    published: z.boolean().optional(),
  });

// Schéma pour récupérer un article
export const getArticleSchema = () =>
  z.object({
    id: z.string().cuid("ID d'article invalide"),
  });

// Schéma pour lister les articles
export const listArticlesSchema = () =>
  z.object({
    page: z.number().min(1).optional().default(1),
    limit: z.number().min(1).max(100).optional().default(10),
    published: z.boolean().optional(),
    authorId: z.string().cuid().optional(),
  });

// Schéma pour supprimer un article
export const deleteArticleSchema = () =>
  z.object({
    id: z.string().cuid("ID d'article invalide"),
  });

// Schémas de réponse
export const articleResponseSchema = () =>
  z.object({
    id: z.string(),
    title: z.string(),
    content: z.string(),
    published: z.boolean(),
    authorId: z.string(),
    author: z.object({
      id: z.string(),
      username: z.string().nullable(),
      email: z.string(),
    }),
    createdAt: z.date(),
    updatedAt: z.date(),
  });

export const createArticleResponseSchema = () =>
  z.object({
    success: z.boolean(),
    article: articleResponseSchema(),
  });

export const updateArticleResponseSchema = () =>
  z.object({
    success: z.boolean(),
    article: articleResponseSchema(),
  });

export const listArticlesResponseSchema = () =>
  z.object({
    articles: z.array(articleResponseSchema()),
    pagination: z.object({
      page: z.number(),
      limit: z.number(),
      total: z.number(),
      totalPages: z.number(),
    }),
  });

export const deleteArticleResponseSchema = () =>
  z.object({
    success: z.boolean(),
    message: z.string(),
  });
```

##### Étape 3 : Créer les fonctions métier (mutations)

```typescript
// src/api/articles/mutations.ts
import { prisma } from "@/lib/prisma";
import { ApiError, handleApiError } from "@/lib/utils/server-utils";
import { ensureLoggedIn } from "@/lib/utils/auth-utils";
import { apiInputFromSchema } from "@/types";
import {
  createArticleSchema,
  updateArticleSchema,
  deleteArticleSchema,
} from "./schemas";

// Mutation pour créer un article
export const createArticle = async ({
  input,
  ctx: { session },
}: apiInputFromSchema<typeof createArticleSchema>) => {
  try {
    // Vérification de l'authentification
    ensureLoggedIn(session);

    const { title, content, published } = input;

    // Création de l'article en base
    const article = await prisma.article.create({
      data: {
        title,
        content,
        published,
        authorId: session.user.id,
      },
      include: {
        author: {
          select: {
            id: true,
            username: true,
            email: true,
          },
        },
      },
    });

    return {
      success: true,
      article,
    };
  } catch (error) {
    return handleApiError(error);
  }
};

// Mutation pour mettre à jour un article
export const updateArticle = async ({
  input,
  ctx: { session },
}: apiInputFromSchema<typeof updateArticleSchema>) => {
  try {
    ensureLoggedIn(session);

    const { id, ...updateData } = input;

    // Vérifier que l'article existe et appartient à l'utilisateur
    const existingArticle = await prisma.article.findUnique({
      where: { id },
      select: { authorId: true },
    });

    if (!existingArticle) {
      return ApiError("articleNotFound", "NOT_FOUND");
    }

    // Vérifier que l'utilisateur est le propriétaire ou admin
    if (
      existingArticle.authorId !== session.user.id &&
      session.user.role !== "ADMIN"
    ) {
      return ApiError("forbidden", "FORBIDDEN");
    }

    // Mise à jour de l'article
    const article = await prisma.article.update({
      where: { id },
      data: updateData,
      include: {
        author: {
          select: {
            id: true,
            username: true,
            email: true,
          },
        },
      },
    });

    return {
      success: true,
      article,
    };
  } catch (error) {
    return handleApiError(error);
  }
};

// Mutation pour supprimer un article
export const deleteArticle = async ({
  input,
  ctx: { session },
}: apiInputFromSchema<typeof deleteArticleSchema>) => {
  try {
    ensureLoggedIn(session);

    const { id } = input;

    // Vérifier que l'article existe et appartient à l'utilisateur
    const existingArticle = await prisma.article.findUnique({
      where: { id },
      select: { authorId: true, title: true },
    });

    if (!existingArticle) {
      return ApiError("articleNotFound", "NOT_FOUND");
    }

    // Vérifier que l'utilisateur est le propriétaire ou admin
    if (
      existingArticle.authorId !== session.user.id &&
      session.user.role !== "ADMIN"
    ) {
      return ApiError("forbidden", "FORBIDDEN");
    }

    // Suppression de l'article
    await prisma.article.delete({
      where: { id },
    });

    return {
      success: true,
      message: `L'article "${existingArticle.title}" a été supprimé avec succès`,
    };
  } catch (error) {
    return handleApiError(error);
  }
};
```

##### Étape 4 : Créer les queries

```typescript
// src/api/articles/queries.ts
import { prisma } from "@/lib/prisma";
import { ApiError, handleApiError } from "@/lib/utils/server-utils";
import { apiInputFromSchema } from "@/types";
import { getArticleSchema, listArticlesSchema } from "./schemas";

// Query pour récupérer un article
export const getArticle = async ({
  input,
  ctx: { session },
}: apiInputFromSchema<typeof getArticleSchema>) => {
  try {
    const { id } = input;

    const article = await prisma.article.findUnique({
      where: { id },
      include: {
        author: {
          select: {
            id: true,
            username: true,
            email: true,
          },
        },
      },
    });

    if (!article) {
      return ApiError("articleNotFound", "NOT_FOUND");
    }

    // Si l'article n'est pas publié, seul l'auteur ou un admin peut le voir
    if (!article.published) {
      if (!session) {
        return ApiError("unauthorized", "UNAUTHORIZED");
      }

      if (
        article.authorId !== session.user.id &&
        session.user.role !== "ADMIN"
      ) {
        return ApiError("forbidden", "FORBIDDEN");
      }
    }

    return article;
  } catch (error) {
    return handleApiError(error);
  }
};

// Query pour lister les articles avec pagination et filtres
export const listArticles = async ({
  input,
  ctx: { session },
}: apiInputFromSchema<typeof listArticlesSchema>) => {
  try {
    const { page, limit, published, authorId } = input;

    const skip = (page - 1) * limit;

    // Construction des filtres
    const where: any = {};

    // Si un utilisateur spécifique est demandé
    if (authorId) {
      where.authorId = authorId;
    }

    // Gestion de la visibilité des articles
    if (published !== undefined) {
      where.published = published;
    } else {
      // Si pas d'utilisateur connecté, ne montrer que les articles publiés
      if (!session) {
        where.published = true;
      } else {
        // Si utilisateur connecté mais pas admin, montrer :
        // - Tous les articles publiés
        // - Ses propres articles (publiés ou non)
        if (session.user.role !== "ADMIN") {
          where.OR = [{ published: true }, { authorId: session.user.id }];
        }
        // Si admin, montrer tous les articles (pas de filtre supplémentaire)
      }
    }

    // Récupération des articles avec pagination
    const [articles, total] = await Promise.all([
      prisma.article.findMany({
        where,
        include: {
          author: {
            select: {
              id: true,
              username: true,
              email: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
        skip,
        take: limit,
      }),
      prisma.article.count({ where }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      articles,
      pagination: {
        page,
        limit,
        total,
        totalPages,
      },
    };
  } catch (error) {
    return handleApiError(error);
  }
};
```

##### Étape 5 : Assembler le routeur

```typescript
// src/api/articles/_router.ts
import {
  router,
  publicProcedure,
  authenticatedProcedure,
} from "@/lib/server/trpc";
import {
  createArticleSchema,
  updateArticleSchema,
  deleteArticleSchema,
  getArticleSchema,
  listArticlesSchema,
  createArticleResponseSchema,
  updateArticleResponseSchema,
  deleteArticleResponseSchema,
  articleResponseSchema,
  listArticlesResponseSchema,
} from "./schemas";
import { createArticle, updateArticle, deleteArticle } from "./mutations";
import { getArticle, listArticles } from "./queries";

export const articlesRouter = router({
  // Queries (lecture de données)

  // Query publique - Récupérer un article
  getArticle: publicProcedure
    .input(getArticleSchema())
    .output(articleResponseSchema())
    .query(getArticle),

  // Query publique - Lister les articles avec pagination
  listArticles: publicProcedure
    .input(listArticlesSchema())
    .output(listArticlesResponseSchema())
    .query(listArticles),

  // Mutations (modification de données)

  // Mutation authentifiée - Créer un article
  createArticle: authenticatedProcedure
    .input(createArticleSchema())
    .output(createArticleResponseSchema())
    .mutation(createArticle),

  // Mutation authentifiée - Mettre à jour un article
  updateArticle: authenticatedProcedure
    .input(updateArticleSchema())
    .output(updateArticleResponseSchema())
    .mutation(updateArticle),

  // Mutation authentifiée - Supprimer un article
  deleteArticle: authenticatedProcedure
    .input(deleteArticleSchema())
    .output(deleteArticleResponseSchema())
    .mutation(deleteArticle),
});
```

##### Étape 6 : Ajouter le routeur à l'app principale

```typescript
// src/api/_app.ts
import { router } from "../lib/server/trpc";
import { authRouter } from "./auth/_router";
import { meRouter } from "./me/_router";
import { uploadRouter } from "./upload/_router";
import { articlesRouter } from "./articles/_router"; // Nouveau routeur

export const appRouter = router({
  auth: authRouter,
  me: meRouter,
  upload: uploadRouter,
  articles: articlesRouter, // Ajout du routeur articles
});

export type AppRouter = typeof appRouter;
```

#### 3. Utilisation côté client

##### Dans un composant React

```typescript
// src/components/articles/article-list.tsx
"use client"
import { trpc } from "@/lib/trpc/client"
import { useState } from "react"

export default function ArticleList() {
  const [page, setPage] = useState(1)

  // Query pour récupérer les articles
  const {
    data: articlesData,
    isLoading,
    error
  } = trpc.articles.listArticles.useQuery({
    page,
    limit: 10,
    published: true, // Seulement les articles publiés
  })

  // Mutation pour supprimer un article
  const deleteArticle = trpc.articles.deleteArticle.useMutation({
    onSuccess: () => {
      // Invalider le cache pour refetch les données
      trpc.articles.listArticles.invalidate()
    },
    onError: (error) => {
      console.error("Erreur lors de la suppression:", error.message)
    }
  })

  const handleDelete = (articleId: string) => {
    if (confirm("Êtes-vous sûr de vouloir supprimer cet article ?")) {
      deleteArticle.mutate({ id: articleId })
    }
  }

  if (isLoading) return <div>Chargement des articles...</div>
  if (error) return <div>Erreur: {error.message}</div>
  if (!articlesData) return <div>Aucun article trouvé</div>

  return (
    <div>
      <h2>Articles ({articlesData.pagination.total})</h2>

      {articlesData.articles.map((article) => (
        <div key={article.id} className="border p-4 mb-4">
          <h3>{article.title}</h3>
          <p>{article.content.substring(0, 200)}...</p>
          <p>
            Par {article.author.username || article.author.email}
            le {new Date(article.createdAt).toLocaleDateString()}
          </p>
          <button
            onClick={() => handleDelete(article.id)}
            disabled={deleteArticle.isLoading}
            className="bg-red-500 text-white px-4 py-2 rounded"
          >
            {deleteArticle.isLoading ? "Suppression..." : "Supprimer"}
          </button>
        </div>
      ))}

      {/* Pagination */}
      <div className="flex gap-2 mt-4">
        <button
          onClick={() => setPage(p => Math.max(1, p - 1))}
          disabled={page === 1}
          className="px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
        >
          Précédent
        </button>
        <span className="px-4 py-2">
          Page {page} sur {articlesData.pagination.totalPages}
        </span>
        <button
          onClick={() => setPage(p => p + 1)}
          disabled={page >= articlesData.pagination.totalPages}
          className="px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
        >
          Suivant
        </button>
      </div>
    </div>
  )
}
```

##### Formulaire de création d'article

```typescript
// src/components/articles/create-article-form.tsx
"use client"
import { trpc } from "@/lib/trpc/client"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { createArticleSchema } from "@/api/articles/schemas"
import { z } from "zod"

type CreateArticleForm = z.infer<ReturnType<typeof createArticleSchema>>

export default function CreateArticleForm() {
  const form = useForm<CreateArticleForm>({
    resolver: zodResolver(createArticleSchema()),
    defaultValues: {
      title: "",
      content: "",
      published: false,
    }
  })

  const createArticle = trpc.articles.createArticle.useMutation({
    onSuccess: (data) => {
      console.log("Article créé:", data.article)
      form.reset()
      // Invalider le cache des articles
      trpc.articles.listArticles.invalidate()
    },
    onError: (error) => {
      console.error("Erreur:", error.message)
    }
  })

  const onSubmit = (data: CreateArticleForm) => {
    createArticle.mutate(data)
  }

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
      <div>
        <label htmlFor="title">Titre</label>
        <input
          id="title"
          {...form.register("title")}
          className="w-full border p-2 rounded"
          placeholder="Titre de l'article"
        />
        {form.formState.errors.title && (
          <p className="text-red-500">{form.formState.errors.title.message}</p>
        )}
      </div>

      <div>
        <label htmlFor="content">Contenu</label>
        <textarea
          id="content"
          {...form.register("content")}
          rows={10}
          className="w-full border p-2 rounded"
          placeholder="Contenu de l'article"
        />
        {form.formState.errors.content && (
          <p className="text-red-500">{form.formState.errors.content.message}</p>
        )}
      </div>

      <div>
        <label className="flex items-center gap-2">
          <input
            type="checkbox"
            {...form.register("published")}
          />
          Publier immédiatement
        </label>
      </div>

      <button
        type="submit"
        disabled={createArticle.isLoading}
        className="bg-blue-500 text-white px-6 py-2 rounded disabled:opacity-50"
      >
        {createArticle.isLoading ? "Création..." : "Créer l'article"}
      </button>
    </form>
  )
}
```

#### 4. Utilisation côté serveur

```typescript
// src/app/[lang]/articles/[id]/page.tsx
import { serverTrpc } from "@/lib/trpc/server"
import { notFound } from "next/navigation"

interface ArticlePageProps {
  params: {
    id: string
    lang: string
  }
}

export default async function ArticlePage({ params }: ArticlePageProps) {
  try {
    // Appel direct côté serveur
    const article = await serverTrpc.articles.getArticle({ id: params.id })

    return (
      <div>
        <h1>{article.title}</h1>
        <p>
          Par {article.author.username || article.author.email}
          le {new Date(article.createdAt).toLocaleDateString()}
        </p>
        <div className="mt-4">
          {article.content.split('\n').map((paragraph, index) => (
            <p key={index} className="mb-4">{paragraph}</p>
          ))}
        </div>
      </div>
    )
  } catch (error) {
    // Si l'article n'existe pas ou n'est pas accessible
    notFound()
  }
}
```

#### 5. Points clés de sécurité

1. **Authentification** : Utilisation de `authenticatedProcedure` pour les opérations sensibles
2. **Autorisation** : Vérification que l'utilisateur peut modifier/supprimer ses propres articles
3. **Validation** : Schémas Zod pour valider toutes les entrées
4. **Gestion d'erreurs** : Erreurs typées et gestion centralisée
5. **Visibilité** : Logique pour afficher seulement les articles appropriés selon l'utilisateur

#### 6. Bonnes pratiques

1. **Séparation des responsabilités** : Schémas, mutations, queries et routeur dans des fichiers séparés
2. **Types sûrs** : Utilisation de TypeScript et Zod pour la validation
3. **Réutilisabilité** : Fonctions métier réutilisables
4. **Performance** : Pagination et sélection optimisée des champs
5. **UX** : Gestion des états de chargement et d'erreur côté client

Cette approche vous donne un routeur tRPC complet, sécurisé et prêt pour la production !

## Authentification avec NextAuth

Le projet utilise NextAuth pour l'authentification avec une stratégie JWT et un adapter Prisma.

### Configuration (`src/lib/auth/index.ts`)

#### 1. Providers d'authentification

```typescript
import NextAuth from "next-auth";
import Credentials from "next-auth/providers/credentials";
import GithubProvider from "next-auth/providers/github";

export const providers: Provider[] = [
  // Authentification par email/mot de passe
  Credentials({
    name: "credentials",
    credentials: {
      email: { label: "Email", type: "email" },
      password: { label: "Password", type: "password" },
    },
    async authorize(credentials, req) {
      // Validation des credentials
      const parsed = signInSchema().safeParse(credentials);
      if (!parsed.success) return null;

      const { email, password, otp } = parsed.data;

      // Recherche de l'utilisateur
      const user = await prisma.user.findUnique({
        where: { email: email.toLowerCase() },
      });

      if (!user || !user.password) return null;

      // Vérification du mot de passe
      const isValidPassword = await bcryptCompare(password, user.password);
      if (!isValidPassword) return null;

      // Vérification 2FA si activé
      if (user.otpVerified && user.otpSecret) {
        if (!otp) return null;

        const totp = new OTPAuth.TOTP({
          secret: user.otpSecret,
          algorithm: "SHA1",
          digits: 6,
          period: 30,
        });

        const isValidOTP = totp.validate({ token: otp, window: otpWindow });
        if (isValidOTP === null) return null;
      }

      return {
        id: user.id.toString(),
        email: user.email,
        username: user.username,
        role: user.role,
        emailVerified: user.emailVerified,
        hasPassword: user.hasPassword,
      };
    },
  }),

  // GitHub OAuth (si activé)
  GithubProvider({
    clientId: env.GITHUB_CLIENT_ID,
    clientSecret: env.GITHUB_CLIENT_SECRET,
  }),
];
```

#### 2. Configuration NextAuth

```typescript
export const { auth, handlers, signIn, signOut } = NextAuth({
  secret: env.NEXTAUTH_SECRET,
  adapter: PrismaAdapter(prisma), // Stockage en base
  providers,

  callbacks: {
    // Callback JWT - Exécuté à chaque création/mise à jour du token
    jwt: async ({ token, user }) => {
      if (user) {
        token.id = user.id;
        token.email = user.email;
        token.username = user.username;
        token.role = user.role;
        token.uuid = user.uuid;
        token.hasPassword = user.hasPassword;
        token.emailVerified = user.emailVerified;
      }
      return token;
    },

    // Callback Session - Exécuté à chaque accès à la session
    session: async ({ session, token }) => {
      // Récupération des données utilisateur fraîches
      const dbUser = await prisma.user.findUnique({
        where: { id: token.id as string },
      });

      if (!dbUser) return session;

      return {
        ...session,
        user: {
          ...session.user,
          id: token.id,
          username: dbUser.username,
          role: dbUser.role,
          uuid: token.uuid,
          hasPassword: dbUser.hasPassword,
          emailVerified: dbUser.emailVerified,
        },
      };
    },

    // Callback SignIn - Contrôle d'accès
    signIn: async ({ user, credentials, account }) => {
      // Logique de contrôle d'accès
      return true;
    },
  },

  pages: {
    signIn: "/sign-in",
    newUser: "/sign-up",
    error: "/sign-in",
  },

  session: {
    strategy: "jwt", // JWT requis pour Credentials provider
    maxAge: SESSION_MAX_AGE, // 30 jours
  },
});
```

### Utilisation de l'authentification

#### 1. Côté serveur

```typescript
import { auth } from "@/lib/auth"

// Dans une Server Action ou API Route
export async function getServerSideProps() {
  const session = await auth()

  if (!session) {
    return { redirect: { destination: "/sign-in" } }
  }

  return {
    props: {
      user: session.user
    }
  }
}

// Dans un Server Component
export default async function ProtectedPage() {
  const session = await auth()

  if (!session) {
    redirect("/sign-in")
  }

  return <div>Bonjour {session.user.username}</div>
}
```

#### 2. Côté client

```typescript
"use client"
import { useSession, signIn, signOut } from "next-auth/react"

export default function ClientComponent() {
  const { data: session, status } = useSession()

  if (status === "loading") return <div>Chargement...</div>

  if (!session) {
    return (
      <button onClick={() => signIn()}>
        Se connecter
      </button>
    )
  }

  return (
    <div>
      <p>Connecté en tant que {session.user.email}</p>
      <button onClick={() => signOut()}>
        Se déconnecter
      </button>
    </div>
  )
}
```

## Base de données avec Prisma

Le projet utilise Prisma comme ORM avec PostgreSQL comme base de données.

### Schéma de base de données (`prisma/schema.prisma`)

#### Modèles principaux

```prisma
// Utilisateurs
model User {
  id               String    @id @default(cuid())
  name             String?
  email            String?   @unique
  emailVerified    DateTime?
  profilePictureId String?   @unique
  profilePicture   File?     @relation(fields: [profilePictureId], references: [id])
  image            String?   // Requis pour auth.js
  accounts         Account[]

  // Champs personnalisés
  username                   String?                     @unique
  role                       UserRole                    @default(USER)
  password                   String?
  hasPassword                Boolean                     @default(false)
  resetPasswordToken         ResetPassordToken?
  userEmailVerificationToken UserEmailVerificationToken?
  lastLocale                 String?
  otpSecret                  String                      @default("")
  otpMnemonic                String                      @default("")
  otpVerified                Boolean                     @default(false)
  uploadsInProgress          FileUploading[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// Comptes OAuth
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

// Rôles utilisateur
enum UserRole {
  ADMIN
  USER
}

// Fichiers
model File {
  id        String   @id @default(cuid())
  key       String   @unique
  filetype  String
  bucket    String
  endpoint  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  userProfilePicture User?
  fileUploadingId    String?        @unique
  fileUploading      FileUploading? @relation(fields: [fileUploadingId], references: [id])
}
```

### Configuration Prisma (`src/lib/prisma.ts`)

```typescript
import { PrismaClient } from "@prisma/client";

const globalForPrisma = global as unknown as { prisma: PrismaClient };

export const prisma =
  globalForPrisma.prisma ||
  new PrismaClient().$extends({
    query: {
      user: {
        async $allOperations({ args, query }) {
          const result = await query(args);

          // Suppression automatique du mot de passe des résultats
          if (
            !("select" in args) ||
            (args.select &&
              typeof args.select === "object" &&
              "password" in args.select &&
              args.select.password !== true)
          ) {
            if (Array.isArray(result)) {
              result.forEach((r) => "password" in r && delete r.password);
            } else if (
              typeof result === "object" &&
              result &&
              "password" in result
            ) {
              delete result.password;
            }
          }
          return result;
        },
      },
    },
  });

if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;
```

### Migrations et seeding

#### 1. Commandes Prisma

```bash
# Générer le client Prisma
npx prisma generate

# Créer une migration
npx prisma migrate dev --name nom_migration

# Ouvrir Prisma Studio
npx prisma studio
```

#### 2. Seeding (`prisma/seed.ts`)

```typescript
import { PrismaClient, UserRole } from "@prisma/client";
import { hash } from "bcryptjs";
import { env } from "../src/lib/env";

const prisma = new PrismaClient();

async function main() {
  // Création de l'utilisateur admin
  const adminEmail = env.AUTH_ADMIN_EMAIL;
  const adminPassword = env.AUTH_ADMIN_PASSWORD;

  const existingAdmin = await prisma.user.findUnique({
    where: { email: adminEmail },
  });

  if (!existingAdmin) {
    const hashedPassword = await hash(adminPassword, 12);

    await prisma.user.create({
      data: {
        email: adminEmail,
        username: "admin",
        password: hashedPassword,
        hasPassword: true,
        role: UserRole.ADMIN,
        emailVerified: new Date(),
      },
    });

    console.log("Utilisateur admin créé");
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
```

---

## Sécurité du projet

### 1. Authentification et autorisation

#### Middleware d'authentification

```typescript
// Vérification de l'authentification
const isAuthenticated = middleware(async (opts) => {
  const { session } = await getAuthApi();

  if (!session) {
    await ApiError("unauthorized", "UNAUTHORIZED");
  }

  return opts.next({
    ctx: { ...opts.ctx, session },
  });
});

// Vérification de l'email
const hasVerifiedEmail = middleware(async (opts) => {
  const { ctx } = opts;
  const session = ctx.session as Session & { user: User };

  if (
    !session ||
    (!session.user.emailVerified && env.NEXT_PUBLIC_ENABLE_MAILING_SERVICE)
  ) {
    await ApiError("emailNotVerified", "UNAUTHORIZED", {
      redirect: false,
    });
  }

  return opts.next();
});
```

#### Protection des routes

```typescript
// Middleware Next.js pour protection des routes
export default auth((req) => {
  const { pathname } = req.nextUrl;

  // Routes protégées
  if (pathname.startsWith("/dashboard")) {
    if (!req.auth) {
      return Response.redirect(new URL("/sign-in", req.url));
    }
  }

  // Routes admin uniquement
  if (pathname.startsWith("/admin")) {
    if (!req.auth || req.auth.user.role !== "ADMIN") {
      return Response.redirect(new URL("/", req.url));
    }
  }
});
```

### 2. Rate Limiting

#### Configuration Redis pour rate limiting

```typescript
import { redis } from "@/lib/redis";

export const rateLimiter = async (
  key: string,
  limit: number,
  windowInSeconds: number
): Promise<{ success: boolean; remaining: number }> => {
  const current = await redis.incr(key);

  if (current === 1) {
    await redis.expire(key, windowInSeconds);
  }

  const remaining = Math.max(0, limit - current);

  return {
    success: current <= limit,
    remaining,
  };
};

// Utilisation dans les procédures tRPC
export const apiRateLimiter = async (req: Request) => {
  const ip = getClientIP(req);
  const { success, remaining } = await rateLimiter(
    `api:${ip}`,
    100, // 100 requêtes
    3600 // par heure
  );

  return {
    headers: {
      "X-RateLimit-Remaining": remaining.toString(),
      "X-RateLimit-Limit": "100",
    },
    success,
  };
};
```

### 3. Validation des données

#### Schémas Zod pour validation

```typescript
import { z } from "zod";

// Schéma de connexion
export const signInSchema = () =>
  z.object({
    email: z.string().email("Email invalide"),
    password: z.string().min(8, "Mot de passe trop court"),
    otp: z.string().optional(),
  });

// Schéma d'inscription
export const signUpSchema = () =>
  z.object({
    email: z.string().email("Email invalide"),
    username: z
      .string()
      .min(3, "Nom d'utilisateur trop court")
      .max(20, "Nom d'utilisateur trop long")
      .regex(/^[a-zA-Z0-9_-]+$/, "Caractères invalides"),
    password: z
      .string()
      .min(8, "Minimum 8 caractères")
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
        "Doit contenir majuscule, minuscule et chiffre"
      ),
    locale: z.string().optional(),
  });
```

### 4. Chiffrement et hachage

#### Gestion des mots de passe

```typescript
import bcrypt from "bcryptjs";

// Hachage du mot de passe
export const bcryptHash = async (password: string): Promise<string> => {
  return await bcrypt.hash(password, 12);
};

// Vérification du mot de passe
export const bcryptCompare = async (
  password: string,
  hashedPassword: string
): Promise<boolean> => {
  return await bcrypt.compare(password, hashedPassword);
};
```

#### Authentification à deux facteurs (2FA)

```typescript
// apps/app/src/api/auth/mutations.ts
import * as OTPAuth from "otpauth";
import { generateMnemonic } from "bip39";

// Génération du secret TOTP
export const generateTotpSecret = async ({ ctx: { session } }) => {
  const secret = OTPAuth.Secret.fromRandom();
  const mnemonic = generateMnemonic();

  // Sauvegarde en base
  await prisma.user.update({
    where: { id: session.user.id },
    data: {
      otpSecret: secret.base32,
      otpMnemonic: mnemonic,
    },
  });

  // Génération du QR code
  const totp = new OTPAuth.TOTP({
    issuer: "NomDuProjet",
    label: session.user.email,
    algorithm: "SHA1",
    digits: 6,
    period: 30,
    secret: secret,
  });

  return {
    qrCode: totp.toString(),
    secret: secret.base32,
    mnemonic,
  };
};

// Vérification du code TOTP
export const verifyTotp = async ({
  input,
  ctx: { session },
}: apiInputFromSchema<typeof verifyTotpSchema>) => {
  ensureLoggedIn(session);
  try {
    const { token } = verifyTotpSchema().parse(input);
    const user = await prisma.user.findFirst({
      where: {
        email: session.user.email,
      },
    });
    if (!user) return ApiError("userNotFound");
    if (!user.otpSecret) return ApiError("otpSecretNotFound");

    const totp = new OTPAuth.TOTP({
      algorithm: "SHA1",
      digits: 6,
      period: 30,
      secret: user.otpSecret,
    });
    const isValid =
      totp.validate({
        token,
        window: otpWindow,
      }) !== null;
    if (user.otpVerified === false && isValid) {
      await prisma.user.update({
        where: {
          id: session.user.id,
        },
        data: {
          otpVerified: true,
        },
      });
    }

    if (!isValid) return ApiError("otpInvalid");
    return { success: true };
  } catch (error: unknown) {
    return handleApiError(error);
  }
};
```

## Gestion des utilisateurs

### Récupération des informations utilisateur

#### 1. Côté serveur (tRPC)

```typescript
// Dans une procédure tRPC
export const getAccount = async ({ ctx: { session } }) => {
  ensureLoggedIn(session) // Vérification de l'authentification

  const user = await prisma.user.findUnique({
    where: { id: session.user.id },
    include: {
      profilePicture: true,
    }
  })

  if (!user) {
    return ApiError("userNotFound")
  }

  return {
    id: user.id,
    email: user.email,
    username: user.username,
    role: user.role,
    emailVerified: user.emailVerified,
    profilePicture: user.profilePicture,
    otpVerified: user.otpVerified,
    createdAt: user.createdAt,
  }
}

// Utilisation dans un Server Component
import { serverTrpc } from "@/lib/trpc/server"

export default async function UserDashboard() {
  const user = await serverTrpc.me.getAccount()

  return (
    <div>
      <h1>Tableau de bord de {user.username}</h1>
      <p>Email: {user.email}</p>
      <p>Rôle: {user.role}</p>
    </div>
  )
}
```

#### 2. Côté client (React)

```typescript
"use client"
import { trpc } from "@/lib/trpc/client"
import { useSession } from "next-auth/react"

export default function UserProfile() {
  const { data: session } = useSession()
  const { data: user, isLoading } = trpc.me.getAccount.useQuery()

  if (!session) {
    return <div>Non connecté</div>
  }

  if (isLoading) {
    return <div>Chargement...</div>
  }

  return (
    <div>
      <h2>Profil utilisateur</h2>
      <p>Nom d'utilisateur: {user?.username}</p>
      <p>Email: {user?.email}</p>
      <p>Rôle: {user?.role}</p>
      <p>Email vérifié: {user?.emailVerified ? "Oui" : "Non"}</p>
      <p>2FA activé: {user?.otpVerified ? "Oui" : "Non"}</p>
    </div>
  )
}
```

#### 3. Hook personnalisé pour l'utilisateur

```typescript
// src/hooks/account.tsx
import { trpc } from "@/lib/trpc/client";

export function useAccount(extendedOptions?: { initialData?: User }) {
  const account = trpc.me.getAccount.useQuery(undefined, {
    initialData: extendedOptions?.initialData,
  });

  return account;
}

// Utilisation
export default function MyComponent() {
  const { data: user, isLoading, error } = useAccount();

  // ...
}
```

### Mise à jour des informations utilisateur

#### 1. Mutation tRPC

```typescript
export const updateUser = async ({
  input,
  ctx: { session },
}: apiInputFromSchema<typeof updateUserSchema>) => {
  ensureLoggedIn(session);

  const { username, email } = input;

  // Vérification de l'unicité
  if (username) {
    const existingUser = await prisma.user.findFirst({
      where: {
        username,
        id: { not: session.user.id },
      },
    });

    if (existingUser) {
      return ApiError("usernameAlreadyExists");
    }
  }

  const updatedUser = await prisma.user.update({
    where: { id: session.user.id },
    data: {
      username,
      email: email?.toLowerCase(),
    },
    include: {
      profilePicture: true,
    },
  });

  return updatedUser;
};
```

#### 2. Utilisation côté client

```typescript
"use client"
import { trpc } from "@/lib/trpc/client"
import { useForm } from "react-hook-form"

export default function EditProfile() {
  const { data: user } = trpc.me.getAccount.useQuery()
  const updateUser = trpc.me.updateUser.useMutation({
    onSuccess: () => {
      // Invalidation du cache pour refetch les données
      trpc.me.getAccount.invalidate()
      toast.success("Profil mis à jour")
    },
    onError: (error) => {
      toast.error(error.message)
    }
  })

  const { register, handleSubmit } = useForm({
    defaultValues: {
      username: user?.username || "",
      email: user?.email || "",
    }
  })

  const onSubmit = (data) => {
    updateUser.mutate(data)
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <input
        {...register("username")}
        placeholder="Nom d'utilisateur"
      />
      <input
        {...register("email")}
        type="email"
        placeholder="Email"
      />
      <button
        type="submit"
        disabled={updateUser.isLoading}
      >
        {updateUser.isLoading ? "Mise à jour..." : "Sauvegarder"}
      </button>
    </form>
  )
}
```

### Gestion des rôles et permissions

#### 1. Vérification des rôles

```typescript
// Utilitaire de vérification des rôles
export const hasRole = (user: User, role: UserRole): boolean => {
  return user.role === role;
};

export const isAdmin = (user: User): boolean => {
  return hasRole(user, UserRole.ADMIN);
};

// Middleware pour les routes admin
const isAdmin = middleware(async (opts) => {
  const { ctx } = opts;
  const session = ctx.session as Session & { user: User };

  if (!session || session.user.role !== UserRole.ADMIN) {
    await ApiError("forbidden", "FORBIDDEN");
  }

  return opts.next();
});

export const adminProcedure = authenticatedProcedure.use(isAdmin);
```

#### 2. Composant de protection par rôle (exemple)

```typescript
"use client"
import { useSession } from "next-auth/react"
import { UserRole } from "@prisma/client"

interface RoleGuardProps {
  children: React.ReactNode
  allowedRoles: UserRole[]
  fallback?: React.ReactNode
}

export default function RoleGuard({
  children,
  allowedRoles,
  fallback = <div>Accès refusé</div>
}: RoleGuardProps) {
  const { data: session } = useSession()

  if (!session) {
    return fallback
  }

  if (!allowedRoles.includes(session.user.role as UserRole)) {
    return fallback
  }

  return <>{children}</>
}

// Utilisation
<RoleGuard allowedRoles={[UserRole.ADMIN]}>
  <AdminPanel />
</RoleGuard>
```

---

## Services externes

### 1. Stockage S3

#### Configuration

```typescript
// src/lib/s3.ts
import { S3Client } from "@aws-sdk/client-s3";
import { env } from "./env";

export const s3Client = env.ENABLE_S3_SERVICE
  ? new S3Client({
      region: env.S3_REGION,
      credentials: {
        accessKeyId: env.S3_ACCESS_KEY_ID ?? "",
        secretAccessKey: env.S3_SECRET_ACCESS_KEY ?? "",
      },
      endpoint: "https://" + env.NEXT_PUBLIC_S3_ENDPOINT,
    })
  : null;
```

#### Upload de fichiers

```typescript
// Génération d'URL pré-signée
export const presignedUrl = async ({
  input,
  ctx: { session },
}: apiInputFromSchema<typeof presignedUrlSchema>) => {
  ensureLoggedIn(session);

  if (!env.ENABLE_S3_SERVICE || !s3Client) {
    return ApiError("s3ServiceDisabled");
  }

  // Rate limiting
  const { success } = await rateLimiter(
    `presigned-url:${session.user.id}`,
    20, // 20 uploads par heure
    60 * 60
  );

  if (!success) {
    return ApiError("tooManyAttempts", "TOO_MANY_REQUESTS");
  }

  const { filename, filetype } = input;
  const key = randomUUID() + "-" + stringToSlug(filename).slice(0, 50);
  const expiresInSeconds = 600; // 10 minutes

  // Enregistrement en base
  await prisma.fileUploading.create({
    data: {
      key,
      userId: session.user.id,
      expires: new Date(Date.now() + expiresInSeconds * 1000),
      bucket: env.NEXT_PUBLIC_S3_BUCKET_NAME!,
      endpoint: env.NEXT_PUBLIC_S3_ENDPOINT!,
      filetype,
    },
  });

  // Génération de l'URL pré-signée
  const { url, fields } = await createPresignedPost(s3Client, {
    Bucket: env.NEXT_PUBLIC_S3_BUCKET_NAME!,
    Key: key,
    Conditions: [
      ["content-length-range", 0, 10 * 1024 * 1024], // 10 MB max
      ["starts-with", "$Content-Type", filetype],
    ],
    Fields: {
      acl: "public-read",
      "Content-Type": filetype,
    },
    Expires: expiresInSeconds,
  });

  return { url, fields };
};
```

#### Composant d'upload

```typescript
"use client"
import { trpc } from "@/lib/trpc/client"
import { useDropzone } from "react-dropzone"

export default function FileUpload() {
  const getPresignedUrl = trpc.upload.presignedUrl.useMutation()

  const onDrop = async (acceptedFiles: File[]) => {
    for (const file of acceptedFiles) {
      try {
        // Obtenir l'URL pré-signée
        const { url, fields } = await getPresignedUrl.mutateAsync({
          filename: file.name,
          filetype: file.type,
        })

        // Upload vers S3
        const formData = new FormData()
        Object.entries(fields).forEach(([key, value]) => {
          formData.append(key, value)
        })
        formData.append("file", file)

        const response = await fetch(url, {
          method: "POST",
          body: formData,
        })

        if (response.ok) {
          toast.success("Fichier uploadé avec succès")
        } else {
          throw new Error("Erreur lors de l'upload")
        }
      } catch (error) {
        toast.error("Erreur lors de l'upload")
      }
    }
  }

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    maxSize: 10 * 1024 * 1024, // 10 MB
  })

  return (
    <div {...getRootProps()} className="border-2 border-dashed p-4">
      <input {...getInputProps()} />
      {isDragActive ? (
        <p>Déposez les fichiers ici...</p>
      ) : (
        <p>Glissez-déposez des fichiers ici, ou cliquez pour sélectionner</p>
      )}
    </div>
  )
}
```

### 2. Service d'email

#### Configuration SMTP

```typescript
// src/lib/mailer.ts
import nodemailer from "nodemailer";
import { env } from "./env";

export const transporter = env.NEXT_PUBLIC_ENABLE_MAILING_SERVICE
  ? nodemailer.createTransporter({
      host: env.SMTP_HOST,
      port: env.SMTP_PORT,
      secure: env.SMTP_PORT === 465,
      auth: {
        user: env.SMTP_USERNAME,
        pass: env.SMTP_PASSWORD,
      },
    })
  : null;

export const sendEmail = async ({
  to,
  subject,
  html,
}: {
  to: string;
  subject: string;
  html: string;
}) => {
  if (!transporter) {
    throw new Error("Service d'email non configuré");
  }

  await transporter.sendMail({
    from: `${env.SMTP_FROM_NAME} <${env.SMTP_FROM_EMAIL}>`,
    to,
    subject,
    html,
  });
};
```

#### Templates d'emails

```typescript
// packages/transactional/emails/verify-email.tsx
import { Html, Head, Body, Container, Text, Link } from "@react-email/components"

interface VerifyEmailProps {
  username: string
  verificationUrl: string
}

export default function VerifyEmail({
  username,
  verificationUrl
}: VerifyEmailProps) {
  return (
    <Html>
      <Head />
      <Body>
        <Container>
          <Text>Bonjour {username},</Text>
          <Text>
            Cliquez sur le lien ci-dessous pour vérifier votre email :
          </Text>
          <Link href={verificationUrl}>
            Vérifier mon email
          </Link>
        </Container>
      </Body>
    </Html>
  )
}

// Utilisation
import { render } from "@react-email/render"
import VerifyEmail from "@nomduprojet/transactional/emails/verify-email"

export const sendVerificationEmail = async ({
  input,
  ctx: { session }
}) => {
  const token = randomUUID()
  const verificationUrl = `${env.NEXT_PUBLIC_BASE_URL}/verify-email?token=${token}`

  // Sauvegarde du token
  await prisma.userEmailVerificationToken.upsert({
    where: { identifier: session.user.id },
    create: {
      identifier: session.user.id,
      token,
      expires: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24h
    },
    update: {
      token,
      expires: new Date(Date.now() + 24 * 60 * 60 * 1000),
    },
  })

  // Envoi de l'email
  const html = render(VerifyEmail({
    username: session.user.username || session.user.email,
    verificationUrl,
  }))

  await sendEmail({
    to: session.user.email,
    subject: "Vérification de votre email",
    html,
  })

  return { success: true }
}
```

### 3. Redis pour le cache

#### Configuration

```typescript
// src/lib/redis.ts
import Redis from "ioredis";
import { env } from "./env";

const redisUrl = env.REDIS_URL;
if (!redisUrl) {
  throw new Error("REDIS_URL is not set");
}

const redisUrlParts = new URL(redisUrl);

export const redis = new Redis({
  host: redisUrlParts.hostname,
  port: parseInt(redisUrlParts.port),
  username: redisUrlParts.username || undefined,
  password: redisUrlParts.password || undefined,
  tls:
    redisUrlParts.protocol === "rediss:"
      ? {
          rejectUnauthorized: false,
        }
      : undefined,
});
```

#### Utilisation pour le cache

```typescript
// Cache des données utilisateur
export const getCachedUser = async (userId: string) => {
  const cached = await redis.get(`user:${userId}`);

  if (cached) {
    return JSON.parse(cached);
  }

  const user = await prisma.user.findUnique({
    where: { id: userId },
  });

  if (user) {
    await redis.setex(`user:${userId}`, 300, JSON.stringify(user)); // 5 min
  }

  return user;
};

// Invalidation du cache
export const invalidateUserCache = async (userId: string) => {
  await redis.del(`user:${userId}`);
};
```

## Développement et bonnes pratiques

### 1. Scripts de développement

#### Scripts principaux

```bash
# Développement
npm run dev              # Lance l'app en mode développement
npm run build           # Build de production
npm run start           # Lance l'app en mode production
npm run type-check      # Vérification TypeScript

# Qualité de code
npm run lint            # ESLint
npm run lint:fix        # ESLint avec correction automatique
npm run prettier        # Vérification Prettier
npm run prettier:fix    # Formatage Prettier

# Tests
npm run test            # Lance les tests Jest

# Base de données
npm run seed            # Seeding de la base de données

# Initialisation
npm run init            # Initialisation complète du projet
npm run init:env        # Configuration des variables d'environnement
npm run init:build      # Build initial
```

**NB:** Exécuter les scripts de qualité de code et celui de la vérification TypeScript avant toute PR.

#### Scripts par workspace

```bash
# Application principale
npm run dev -w apps/app
npm run build -w apps/app

# Cron jobs
npm run dev -w apps/cron
npm run build -w apps/cron

# Packages
npm run build -w packages/lib
npm run build -w packages/transactional
```

### 2. Structure des composants

#### Composants UI réutilisables

```typescript
// src/components/ui/button.tsx
import { cn } from "@/lib/utils"
import { Button as NextUIButton, ButtonProps } from "@nextui-org/button"

interface CustomButtonProps extends ButtonProps {
  variant?: "primary" | "secondary" | "danger"
}

export default function Button({
  className,
  variant = "primary",
  ...props
}: CustomButtonProps) {
  return (
    <NextUIButton
      className={cn(
        "font-medium",
        {
          "bg-primary text-white": variant === "primary",
          "bg-secondary text-gray-900": variant === "secondary",
          "bg-red-500 text-white": variant === "danger",
        },
        className
      )}
      {...props}
    />
  )
}
```

#### Composants avec dictionnaire

```typescript
// src/components/auth/sign-in-form.tsx
"use client"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { signInSchema } from "@/api/auth/schemas"
import { TDictionary } from "@/lib/langs"
import FormField from "@/components/ui/form"

interface SignInFormProps {
  dictionary: TDictionary<{
    email: true
    password: true
    signIn: true
    errors: {
      invalidCredentials: true
    }
  }>
  onSubmit: (data: SignInData) => Promise<void>
}

export default function SignInForm({ dictionary, onSubmit }: SignInFormProps) {
  const form = useForm({
    resolver: zodResolver(signInSchema()),
    defaultValues: {
      email: "",
      password: "",
    }
  })

  return (
    <form onSubmit={form.handleSubmit(onSubmit)}>
      <FormField
        form={form}
        name="email"
        type="email"
        label={dictionary.email}
        dictionary={dictionary}
      />
      <FormField
        form={form}
        name="password"
        type="password-eye-slash"
        label={dictionary.password}
        dictionary={dictionary}
      />
      <Button type="submit" isLoading={form.formState.isSubmitting}>
        {dictionary.signIn}
      </Button>
    </form>
  )
}
```

### 3. Gestion des erreurs

#### Gestion d'erreurs côté serveur

```typescript
// src/lib/utils/server-utils.ts
import { TRPCError } from "@trpc/server";

export type TErrorMessage = {
  key: string;
  extra?: Record<string, unknown>;
};

export const ApiError = async (
  key: string,
  code: TRPCError["code"] = "BAD_REQUEST",
  extra?: Record<string, unknown>
): Promise<never> => {
  const message: TErrorMessage = { key, extra };

  throw new TRPCError({
    code,
    message: JSON.stringify(message),
  });
};

// Gestion des erreurs Prisma
export const handleApiError = (error: unknown) => {
  if (error instanceof PrismaClientKnownRequestError) {
    switch (error.code) {
      case "P2002":
        return ApiError("duplicateEntry");
      case "P2025":
        return ApiError("recordNotFound");
      default:
        return ApiError("databaseError");
    }
  }

  if (error instanceof TRPCError) {
    throw error;
  }

  logger.error("Unexpected error", error);
  return ApiError("internalServerError", "INTERNAL_SERVER_ERROR");
};
```

#### Gestion d'erreurs côté client

```typescript
// src/lib/utils/client-utils.ts
import { TRPCClientErrorLike } from "@trpc/client";
import { AppRouter } from "@/api/_app";
import { toast } from "react-toastify";

export const handleQueryError = (
  error: TRPCClientErrorLike<AppRouter>,
  dictionary: TDictionary,
  router: AppRouterInstance
) => {
  try {
    const errorData = JSON.parse(error.message) as TErrorMessage;
    const errorMessage =
      dictionary.errors?.[errorData.key] || dictionary.unknownError;

    toast.error(errorMessage);

    // Redirection si nécessaire
    if (
      errorData.extra?.redirect &&
      typeof errorData.extra.redirect === "string"
    ) {
      router.push(errorData.extra.redirect);
    }
  } catch {
    toast.error(dictionary.unknownError);
  }
};
```

### 4. Internationalisation

#### Configuration i18n

```typescript
// src/lib/i18n-config.ts
export const i18n = {
  defaultLocale: "fr",
  locales: ["fr", "en"],
} as const;

export type Locale = (typeof i18n)["locales"][number];
```

#### Dictionnaires typés

```typescript
// src/lib/langs.ts
import { Locale } from "./i18n-config";

export type TDictionary<T = Record<string, unknown>> = T;

export const getDictionary = async <T>(
  locale: Locale,
  requirements: T
): Promise<TDictionary<T>> => {
  const dictionary = await import(`@/langs/${locale}.json`).then(
    (module) => module.default
  );

  // Validation que toutes les clés requises sont présentes
  validateDictionary(dictionary, requirements);

  return dictionary as TDictionary<T>;
};

// Utilitaire pour définir les exigences de dictionnaire
export const dictionaryRequirements = <T extends Record<string, unknown>>(
  requirements: T
): T => requirements;
```

#### Middleware de localisation

```typescript
// src/middleware.ts
import { NextRequest, NextResponse } from "next/server";
import { match as matchLocale } from "@formatjs/intl-localematcher";
import Negotiator from "negotiator";
import { i18n } from "@/lib/i18n-config";

function getLocale(request: NextRequest, cookiesLocale?: string): string {
  if (cookiesLocale) return cookiesLocale;

  const negotiatorHeaders: Record<string, string> = {};
  request.headers.forEach((value, key) => (negotiatorHeaders[key] = value));

  const locales: string[] = i18n.locales as unknown as string[];
  const languages = new Negotiator({ headers: negotiatorHeaders }).languages(
    locales
  );

  return matchLocale(languages, locales, i18n.defaultLocale);
}

export function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;

  // Vérifier si le pathname contient déjà une locale
  const pathnameIsMissingLocale = i18n.locales.every(
    (locale) => !pathname.startsWith(`/${locale}/`) && pathname !== `/${locale}`
  );

  // Rediriger si aucune locale n'est présente
  if (pathnameIsMissingLocale) {
    const locale = getLocale(request);
    return NextResponse.redirect(
      new URL(
        `/${locale}${pathname.startsWith("/") ? "" : "/"}${pathname}`,
        request.url
      )
    );
  }
}
```

### 5. Linting et formatage

#### Configuration ESLint

```javascript
// .eslintrc.js
module.exports = {
  extends: [
    "eslint-config-custom",
    "next/core-web-vitals",
    "@typescript-eslint/recommended",
  ],
  plugins: ["eslint-plugin-custom-rule"],
  rules: {
    "custom-rule/no-use-client": "error",
    "custom-rule/handle-api-error": "error",
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/no-explicit-any": "error",
  },
};
```

#### Règles ESLint personnalisées

```javascript
// packages/configs/eslint-plugin-custom-rule/no-use-client.js
module.exports = {
  meta: {
    type: "problem",
    docs: {
      description: "Disallow 'use client' in page or layout files",
    },
  },
  create: function (context) {
    return {
      Program: (node) => {
        const filename = context.filename;
        const sourceCode = context.sourceCode;
        const firstLine = sourceCode.lines[0]?.trim();

        const isTargetedFile = /(?:page|layout|dr)\.(ts|tsx|js|jsx)$/.test(
          filename
        );

        if (isTargetedFile && firstLine === '"use client"') {
          context.report({
            node,
            message: "'use client' is not allowed in page or layout files.",
          });
        }
      },
    };
  },
};
```

---

## Déploiement

### 1. Docker

#### Dockerfile pour l'application

```dockerfile
# apps/app/Dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Copy package files
COPY package.json package-lock.json ./
COPY apps/app/package.json ./apps/app/
COPY packages/*/package.json ./packages/*/

# Install dependencies
RUN npm ci --only=production

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Build the application
RUN npm run build -w apps/app

# Production image
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy built application
COPY --from=builder /app/apps/app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/apps/app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/apps/app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000

CMD ["node", "server.js"]
```

#### Docker Compose

```yaml
# docker/docker-compose.yml
name: nomduprojet
services:
  app:
    image: nomduprojet/app:latest
    container_name: nomduprojet_app
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - DATABASE_PRISMA_URL=${DATABASE_PRISMA_URL}
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      - REDIS_URL=${REDIS_URL}
    depends_on:
      - db
      - redis

  cron:
    image: nomduprojet/cron:latest
    container_name: nomduprojet_cron
    restart: unless-stopped
    environment:
      - DATABASE_PRISMA_URL=${DATABASE_PRISMA_URL}
    depends_on:
      - db

  db:
    image: postgres:15
    container_name: nomduprojet_db
    restart: unless-stopped
    environment:
      - POSTGRES_USER=${DATABASE_USER}
      - POSTGRES_PASSWORD=${DATABASE_PASS}
      - POSTGRES_DB=${DATABASE_NAME}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    container_name: nomduprojet_redis
    restart: unless-stopped
    command: redis-server --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"

volumes:
  postgres_data:
  redis_data:
```

### 2. Variables d'environnement de production

```bash
# Production .env
ENV=production
NEXT_PUBLIC_BASE_URL=https://yourdomain.com

# Base de données
DATABASE_PRISMA_URL="**********************************/nomduprojet"
DATABASE_URL_NON_POOLING="**********************************/nomduprojet"

# Authentification
NEXTAUTH_SECRET="your-production-secret"
NEXTAUTH_URL="https://yourdomain.com"
PASSWORD_HASHER_SECRET="your-hasher-secret"

# OAuth
GITHUB_CLIENT_ID="your-github-client-id"
GITHUB_CLIENT_SECRET="your-github-client-secret"

# Redis
REDIS_URL="redis://:password@redis:6379"

# Email
SMTP_HOST="smtp.yourdomain.com"
SMTP_PORT="587"
SMTP_USERNAME="<EMAIL>"
SMTP_PASSWORD="your-smtp-password"
SMTP_FROM_NAME="Your App"
SMTP_FROM_EMAIL="<EMAIL>"
NEXT_PUBLIC_ENABLE_MAILING_SERVICE="true"

# S3
S3_REGION="us-east-1"
S3_ACCESS_KEY_ID="your-access-key"
S3_SECRET_ACCESS_KEY="your-secret-key"
NEXT_PUBLIC_S3_ENDPOINT="s3.amazonaws.com"
NEXT_PUBLIC_S3_BUCKET_NAME="your-bucket"
ENABLE_S3_SERVICE="true"

# Sécurité
DISABLE_REGISTRATION="false"
```

### 3. Scripts de déploiement

#### Script de build

```bash
#!/bin/bash
# scripts/build.sh

set -e

echo "🏗️  Building NomDuProjet..."

# Build des packages
echo "📦 Building packages..."
npm run build -w packages/lib
npm run build -w packages/transactional

# Build de l'application
echo "🚀 Building application..."
npm run build -w apps/app

# Build des cron jobs
echo "⏰ Building cron jobs..."
npm run build -w apps/cron

echo "✅ Build completed successfully!"
```

#### Script de déploiement

```bash
#!/bin/bash
# scripts/deploy.sh

set -e

echo "🚀 Deploying NomduProjet..."

# Variables
IMAGE_TAG=${1:-latest}
REGISTRY=${REGISTRY:-your-registry.com}

# Build des images Docker
echo "🐳 Building Docker images..."
docker build -t $REGISTRY/nomduprojet/app:$IMAGE_TAG -f apps/app/Dockerfile .
docker build -t $REGISTRY/nomduprojet/cron:$IMAGE_TAG -f apps/cron/Dockerfile .

# Push des images
echo "📤 Pushing images..."
docker push $REGISTRY/nomduprojet/app:$IMAGE_TAG
docker push $REGISTRY/nomduprojet/cron:$IMAGE_TAG

# Déploiement
echo "🎯 Deploying to production..."
docker-compose -f docker/docker-compose.yml pull
docker-compose -f docker/docker-compose.yml up -d

# Migrations de base de données
echo "🗄️  Running database migrations..."
docker-compose -f docker/docker-compose.yml exec app npx prisma migrate deploy

echo "✅ Deployment completed successfully!"
```

### 4. Monitoring et logs

#### Configuration des logs

```typescript
// src/lib/logger.ts
import { logger as baseLogger } from "@nomduprojet/lib";

export const logger = baseLogger.child({
  service: "nomduprojet-app",
  version: process.env.npm_package_version,
});

// Middleware de logging pour tRPC
export const loggingMiddleware = middleware(async (opts) => {
  const start = Date.now();

  const result = await opts.next();

  const duration = Date.now() - start;

  logger.info("tRPC call", {
    path: opts.path,
    type: opts.type,
    duration,
    success: result.ok,
  });

  return result;
});
```

#### Health checks

```typescript
// src/app/api/health/route.ts
import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { redis } from "@/lib/redis";

export async function GET() {
  try {
    // Vérification de la base de données
    await prisma.$queryRaw`SELECT 1`;

    // Vérification de Redis
    await redis.ping();

    return NextResponse.json({
      status: "healthy",
      timestamp: new Date().toISOString(),
      services: {
        database: "healthy",
        redis: "healthy",
      },
    });
  } catch (error) {
    return NextResponse.json(
      {
        status: "unhealthy",
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 503 }
    );
  }
}
```

---

## Conclusion

Cette documentation couvre tous les aspects essentiels du boilerplate. Pour un développeur débutant, il est recommandé de :

1. **Commencer par la configuration** : Suivre la section "Configuration de l'environnement"
2. **Comprendre tRPC** : Étudier les exemples d'utilisation côté client et serveur
3. **Maîtriser l'authentification** : Comprendre NextAuth et les middlewares de sécurité
4. **Pratiquer avec Prisma** : Créer des migrations et des requêtes
5. **Développer progressivement** : Commencer par des composants simples puis évoluer

### Ressources utiles

- [Documentation Next.js](https://nextjs.org/docs)
- [Documentation tRPC](https://trpc.io/docs)
- [Documentation Prisma](https://www.prisma.io/docs)
- [Documentation NextAuth](https://next-auth.js.org/)
- [Documentation NextUI](https://nextui.org/)

### Support

Pour toute question ou problème, consultez :

- Les logs de l'application
- Stack Overflow
- La documentation des dépendances utilisées
- Le channel [#help](https://discord.com/channels/772775763132547102/1097399422406643742) du serveur Discord où vous posez clairement votre problème (il n'y a de télépathe parmi nous 😆). Vous pouvez consulter le message épinglé pour savoir comment poser votre préoccupation.

---
